# Prompt Editing Fix for BEI and E-tray Evaluations

## Issue
The BEI and E-tray evaluation components were showing "can't update" when trying to edit prompts. The issue was that the `PromptModal` component was read-only and didn't support editing functionality.

## Root Cause
The `PromptModal` component was originally designed for LGD evaluations as a read-only modal. However, BEI and E-tray components were trying to use it with an `onSave` prop for editing, but the modal didn't handle this prop or provide editing functionality.

## Solution
Enhanced the `PromptModal` component to support both read-only and editable modes:

### Changes Made

1. **Added editing state management**:
   - `isEditing` - tracks whether the modal is in edit mode
   - `editContent` - stores the content being edited
   - `saving` - tracks save operation status

2. **Added editing functionality**:
   - Edit button appears when `onSave` prop is provided
   - Textarea for editing prompt content
   - Save and Cancel buttons in edit mode
   - Proper state management and error handling

3. **Maintained backward compatibility**:
   - LGD evaluations continue to work as read-only modals
   - BEI and E-tray evaluations now have full editing capability

### Key Features

- **Conditional editing**: Edit button only appears when `onSave` prop is provided
- **State management**: Proper handling of editing state and content synchronization
- **Error handling**: Graceful error handling with user feedback
- **UI consistency**: Maintains the existing design while adding editing capabilities
- **Auto-reset**: Editing state resets when modal is closed

### Usage

The modal now supports two modes:

1. **Read-only mode** (LGD evaluations):
   ```jsx
   <PromptModal
     isOpen={isOpen}
     onClose={onClose}
     promptData={promptData}
     promptName={promptName}
   />
   ```

2. **Editable mode** (BEI and E-tray evaluations):
   ```jsx
   <PromptModal
     isOpen={isOpen}
     onClose={onClose}
     promptData={promptData}
     promptName={promptName}
     onSave={(newContent) => handlePromptUpdate(promptId, newContent)}
   />
   ```

## Testing
To test the fix:

1. Navigate to BEI or E-tray evaluation pages
2. Click on "📝 Edit Analysis Prompt" or "📝 Edit Formatting Prompt" buttons
3. The modal should now show an "✏️ Edit" button
4. Click Edit to enter editing mode
5. Make changes to the prompt content
6. Click "💾 Save" to save changes or "Cancel" to discard
7. Verify that the prompt is updated successfully

## Files Modified
- `client/src/components/PromptModal.jsx` - Enhanced with editing functionality
- `client/src/components/BEIEvaluationRunner.jsx` - Fixed PromptModal prop usage (onUpdate → onSave)
- `client/src/components/EtrayEvaluationRunner.jsx` - Fixed React import

## Additional Fix
Fixed the prop mismatch in `BEIEvaluationRunner.jsx` where the PromptModal was being called with `onUpdate` prop instead of `onSave` prop, which prevented the editing functionality from working properly.

## Impact
- ✅ BEI prompt editing now works correctly from the "📝 Edit Analysis Prompt" button
- ✅ E-tray prompt editing now works correctly from the "📝 Edit Analysis Prompt" button
- ✅ LGD read-only functionality preserved
- ✅ Consistent UI/UX across all evaluation types
- ✅ Fixed React import warnings
