# Node modules
node_modules/
client/node_modules/
server/node_modules/

# Build outputs
client/dist/
client/build/

# Development files
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Git
.git/
.gitignore

# Docker
Dockerfile
docker-compose.yml
.dockerignore

# Environment files (we copy .env explicitly)
.env.local
.env.development
.env.test
.env.production

# Documentation
README.md
*.md
