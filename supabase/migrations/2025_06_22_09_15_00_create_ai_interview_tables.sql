-- Create the datasets table for AI Interview question-answer pairs
CREATE TABLE datasets (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    data JSONB NOT NULL,
    "createdAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    "updatedAt" TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create the ai_interview_evaluations table
CREATE TABLE ai_interview_evaluations (
    id SERIAL PRIMARY KEY,
    dataset_id INT REFERENCES datasets(id),
    dataset_name TEXT,
    output JSONB,
    status TEXT DEFAULT 'in_progress',
    "prompt1Version" INT,
    "prompt2Version" INT,
    "prompt3Version" INT,
    "prompt1Content" TEXT,
    "prompt2Content" TEXT,
    "prompt3Content" TEXT,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    details JSONB,
    annotation TEXT
);

-- Add indexes for better performance
CREATE INDEX idx_ai_interview_evaluations_status ON ai_interview_evaluations(status);
CREATE INDEX idx_ai_interview_evaluations_timestamp ON ai_interview_evaluations(timestamp);
CREATE INDEX idx_datasets_created_at ON datasets("createdAt");
