-- Add label column to datasets table to categorize datasets by evaluation type
ALTER TABLE datasets ADD COLUMN label TEXT;

-- Update existing datasets with appropriate labels
-- Sample Interview Dataset should be labeled as 'ai_interview'
UPDATE datasets 
SET label = 'ai_interview' 
WHERE name = 'Sample Interview Dataset';

-- LoI Scoring Dataset should be labeled as 'loi'
UPDATE datasets 
SET label = 'loi' 
WHERE name = 'LoI Scoring Dataset';

-- Set default label for any other existing datasets to 'ai_interview' for backward compatibility
UPDATE datasets 
SET label = 'ai_interview' 
WHERE label IS NULL;

-- Add index for better performance on label filtering
CREATE INDEX idx_datasets_label ON datasets(label);
