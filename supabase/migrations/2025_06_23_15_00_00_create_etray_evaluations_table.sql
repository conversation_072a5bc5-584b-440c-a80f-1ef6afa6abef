-- Create the etray_evaluations table following LGD/BEI pattern
CREATE TABLE etray_evaluations (
    id SERIAL PRIMARY KEY,
    input JSONB,
    "formattedInput" TEXT,
    output JSONB,
    status VARCHAR(50) DEFAULT 'in_progress',
    "prompt1Version" INT,
    "prompt2Version" INT,
    "prompt1Content" TEXT,
    "prompt2Content" TEXT,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    details JSONB,
    annotation TEXT
);

-- Add indexes for better query performance
CREATE INDEX idx_etray_evaluations_timestamp ON etray_evaluations(timestamp);
CREATE INDEX idx_etray_evaluations_status ON etray_evaluations(status);
