-- Create the ai_interview_v2_evaluations table
CREATE TABLE ai_interview_v2_evaluations (
    id SERIAL PRIMARY KEY,
    dataset_id INT REFERENCES datasets(id),
    dataset_name TEXT,
    output JSONB,
    status TEXT DEFAULT 'in_progress',
    "prompt1Version" INT,
    "prompt2Version" INT,
    "prompt1Content" TEXT,
    "prompt2Content" TEXT,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    details JSONB,
    annotation TEXT
);

-- Add indexes for better performance
CREATE INDEX idx_ai_interview_v2_evaluations_status ON ai_interview_v2_evaluations(status);
CREATE INDEX idx_ai_interview_v2_evaluations_timestamp ON ai_interview_v2_evaluations(timestamp); 