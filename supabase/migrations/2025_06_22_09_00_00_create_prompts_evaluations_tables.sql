-- Create the prompts table
CREATE TABLE prompts (
    id INT PRIMARY KEY,
    name TEXT,
    content TEXT,
    version INT,
    "createdAt" TIMESTAMP WITH TIME ZONE,
    "updatedAt" TIMESTAMP WITH TIME ZONE
);

-- Create the evaluations table
CREATE TABLE evaluations (
    id SERIAL PRIMARY KEY,
    input JSONB,
    "formattedInput" TEXT,
    objectives JSONB,
    output JSONB,
    "prompt1Version" INT,
    "prompt2Version" INT,
    "prompt1Content" TEXT,
    "prompt2Content" TEXT,
    timestamp TIMESTAMP WITH TIME ZONE,
    details JSONB
);