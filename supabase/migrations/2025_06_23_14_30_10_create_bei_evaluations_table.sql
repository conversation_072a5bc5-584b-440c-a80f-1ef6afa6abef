-- Create the bei_evaluations table
CREATE TABLE bei_evaluations (
    id SERIAL PRIMARY KEY,
    input JSONB,
    output TEXT,
    status VARCHAR(50) DEFAULT 'in_progress',
    timestamp TIMESTAMP WITH TIME ZONE,
    annotation TEXT,
    details JSONB
);

-- Add index for better query performance
CREATE INDEX idx_bei_evaluations_timestamp ON bei_evaluations(timestamp);
CREATE INDEX idx_bei_evaluations_status ON bei_evaluations(status);
