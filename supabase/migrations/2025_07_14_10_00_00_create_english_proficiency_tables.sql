-- Create the english_proficiency_evaluations table
CREATE TABLE english_proficiency_evaluations (
    id SERIAL PRIMARY KEY,
    dataset_id INT REFERENCES datasets(id),
    dataset_name TEXT,
    output JSONB,
    status TEXT DEFAULT 'in_progress',
    "prompt1Version" INT,
    "prompt2Version" INT,
    "prompt1Content" TEXT,
    "prompt2Content" TEXT,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    details JSONB,
    annotation TEXT
);

-- Add indexes for better performance
CREATE INDEX idx_english_proficiency_evaluations_status ON english_proficiency_evaluations(status);
CREATE INDEX idx_english_proficiency_evaluations_timestamp ON english_proficiency_evaluations(timestamp);
CREATE INDEX idx_english_proficiency_evaluations_dataset_id ON english_proficiency_evaluations(dataset_id);
