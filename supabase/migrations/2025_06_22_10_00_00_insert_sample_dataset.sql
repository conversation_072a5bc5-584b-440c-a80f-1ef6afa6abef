-- Insert a sample dataset for testing AI Interview evaluations
INSERT INTO datasets (name, description, data, label, "createdAt", "updatedAt") VALUES
('Sample Interview Dataset', 'A sample dataset with basic interview questions for testing the AI Interview evaluation system', 
'{
  "data": [
    {
      "question": "Bayangkan Anda sedang menghitung uang kembalian untuk pelanggan, lalu pelanggan mengatakan jumlahnya salah dan mulai menyalahkanmu di depan orang lain. Bagaimana Anda akan menanggapi situasi tersebut?",
      "answer": "Ya, saya akan meminta maaf terlebih dahulu untuk melakukan pengecekan apakah benar kembaliannya itu salah ataupun sudah tepat seperti itu.",
      "answer_url": "https://rakamin-app.s3.ap-southeast-1.amazonaws.com/files/question1response-84aec7e1-3ddb-4f07-b0df-024ca9372928.webm"
    },
    {
      "question": "Bayangkan rekan kerja Anda melakukan kesalahan saat bertuga<PERSON>, tapi atasan justru menyalahkan Anda. Apa yang akan Anda lakukan?",
      "answer": "Iya. Saya akan mencoba mengklarifikasikan terlebih dahulu apa yang terjadi kalau memang itu saya tahu gitu ya kejadiannya dan nanti akan saya sampaikan ke atasan saya apa yang sebenarnya terjadi.",
      "answer_url": "https://rakamin-app.s3.ap-southeast-1.amazonaws.com/files/question2response-e001149d-6296-462c-a316-2c1df01bd1d9.webm"
    },
    {
      "question": "Hari ini merupakan hari pertama Anda bekerja sebagai kasir. Tiba-tiba terdapat antrean panjang. Apa yang akan Anda lakukan?",
      "answer": "Ya, kalau di toko tersebut ada beberapa kasir, gitu kasir komputer, saya akan aktifkan juga kasir yang lain dan saya akan meminta kepada customer untuk bisa mengantri dengan tertib.",
      "answer_url": "https://rakamin-app.s3.ap-southeast-1.amazonaws.com/files/question3response-78ef4ac6-72a5-4e34-9fc1-b5c747dd4078.webm"
    },
    {
      "question": "Jika ada pelanggan yang menanyakan promo khusus tapi Anda belum terlalu hafal dengan detailnya. Apa yang akan Anda lakukan?",
      "answer": "Ya, kalau saya belum tahu untuk detail promonya seperti apa, saya akan minta bantuan ke rekan kerja saya yang mungkin lebih tahu ataupun mungkin atasan saya yang ada di toko untuk membantu saya untuk menjelaskan terkait dengan promo yang dimaksud.",
      "answer_url": "https://rakamin-app.s3.ap-southeast-1.amazonaws.com/files/question4response-867dee03-b2b5-4892-b547-dcdbe3085e49.webm"
    },
    {
      "question": "Anda diminta untuk mengatur ulang barang di rak oleh supervisor Anda. Tapi, Anda belum pernah melakukan hal tersebut. Apa pertanyaan yang akan Anda ajukan dalam situasi tersebut?",
      "answer": "Ya, kalau saya belum tahu untuk detail promonya seperti apa, saya akan minta bantuan ke rekan kerja saya yang mungkin lebih tahu ataupun mungkin atasan saya yang ada di toko untuk membantu saya untuk menjelaskan terkait dengan promo yang dimaksud.",
      "answer_url": "https://rakamin-app.s3.ap-southeast-1.amazonaws.com/files/question4response-867dee03-b2b5-4892-b547-dcdbe3085e49.webm"
    }
  ]
}', 'ai_interview', NOW(), NOW());
