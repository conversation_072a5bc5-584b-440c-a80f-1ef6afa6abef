// Test script to verify BEI analysis follows LGD pattern exactly
const fs = require('fs');
const path = require('path');

async function testBEILGDPattern() {
  try {
    console.log('🧪 Testing BEI Analysis System (LGD Pattern)...\n');

    // Test 1: Verify BEI service has prompt chain method
    console.log('1. Testing BEI service prompt chain method...');
    const beiService = require('./server/services/beiGemini');
    
    if (typeof beiService.runBEIPromptChain === 'function') {
      console.log('✅ BEI service has runBEIPromptChain method');
    } else {
      throw new Error('❌ BEI service missing runBEIPromptChain method');
    }

    // Test 2: Verify BEI routes file structure (without loading)
    console.log('\n2. Testing BEI routes file structure...');
    const routesFile = 'server/routes/beiEvaluations.js';
    if (fs.existsSync(routesFile)) {
      const routesContent = fs.readFileSync(routesFile, 'utf8');
      if (routesContent.includes('processBEIEvaluationAsync') &&
          routesContent.includes('prompt1Content') &&
          routesContent.includes('prompt2Content')) {
        console.log('✅ BEI routes have prompt chain structure');
      } else {
        throw new Error('❌ BEI routes missing prompt chain structure');
      }
    } else {
      throw new Error('❌ BEI routes file not found');
    }

    // Test 3: Check migration files exist
    console.log('\n3. Testing migration files...');
    const migrationFiles = [
      'supabase/migrations/update_bei_evaluations_table.sql',
      'supabase/migrations/insert_bei_prompts.sql'
    ];

    for (const file of migrationFiles) {
      if (fs.existsSync(file)) {
        console.log(`✅ Migration file exists: ${file}`);
      } else {
        throw new Error(`❌ Missing migration file: ${file}`);
      }
    }

    // Test 4: Verify BEI prompts content
    console.log('\n4. Testing BEI prompts content...');
    const promptsFile = 'supabase/migrations/insert_bei_prompts.sql';
    const promptsContent = fs.readFileSync(promptsFile, 'utf8');
    
    if (promptsContent.includes('BEI Analysis Prompt') && promptsContent.includes('BEI Formatting Prompt')) {
      console.log('✅ BEI prompts contain analysis and formatting prompts');
    } else {
      throw new Error('❌ BEI prompts missing required content');
    }

    if (promptsContent.includes('{{ bei_competencies }}')) {
      console.log('✅ BEI analysis prompt has competency placeholder');
    } else {
      throw new Error('❌ BEI analysis prompt missing competency placeholder');
    }

    // Test 5: Check frontend components
    console.log('\n5. Testing frontend components...');
    const runnerFile = 'client/src/components/BEIEvaluationRunner.jsx';
    const resultsFile = 'client/src/components/BEIResultsTable.jsx';

    if (fs.existsSync(runnerFile) && fs.existsSync(resultsFile)) {
      console.log('✅ BEI frontend components exist');
    } else {
      throw new Error('❌ Missing BEI frontend components');
    }

    // Test 6: Verify BEI runner has required fields
    console.log('\n6. Testing BEI runner component structure...');
    const runnerContent = fs.readFileSync(runnerFile, 'utf8');
    
    const requiredFeatures = [
      'transcript',
      'competencies',
      'PromptModal',
      'openPromptModal',
      'handlePromptUpdate'
    ];

    for (const feature of requiredFeatures) {
      if (runnerContent.includes(feature)) {
        console.log(`✅ BEI runner has ${feature}`);
      } else {
        throw new Error(`❌ BEI runner missing ${feature}`);
      }
    }

    // Test 7: Verify BEI results table has LGD pattern features
    console.log('\n7. Testing BEI results table structure...');
    const resultsContent = fs.readFileSync(resultsFile, 'utf8');
    
    const requiredResultsFeatures = [
      'prompt1Version',
      'prompt2Version',
      'PromptModal',
      'competencies',
      'openPromptModal'
    ];

    for (const feature of requiredResultsFeatures) {
      if (resultsContent.includes(feature)) {
        console.log(`✅ BEI results table has ${feature}`);
      } else {
        throw new Error(`❌ BEI results table missing ${feature}`);
      }
    }

    // Test 8: Check data files
    console.log('\n8. Testing data files...');
    const dataFiles = [
      'server/data/bei_transcript.txt',
      'server/data/bei_competencies.txt'
    ];

    for (const file of dataFiles) {
      if (fs.existsSync(file)) {
        console.log(`✅ Data file exists: ${file}`);
      } else {
        console.log(`⚠️  Optional data file missing: ${file}`);
      }
    }

    console.log('\n🎉 All BEI system tests passed!');
    console.log('\n📋 Summary of BEI Analysis System (LGD Pattern):');
    console.log('✅ Two-step prompt chain (Analysis + Formatting)');
    console.log('✅ Editable transcript field');
    console.log('✅ Editable competencies field');
    console.log('✅ Editable prompts with modal interface');
    console.log('✅ Prompt versioning and tracking');
    console.log('✅ Results display with prompt information');
    console.log('✅ Database structure matching LGD pattern');
    console.log('✅ Sample data loading functionality');

    console.log('\n🚀 Next steps:');
    console.log('1. Run Supabase migrations:');
    console.log('   - update_bei_evaluations_table.sql');
    console.log('   - insert_bei_prompts.sql');
    console.log('2. Start the server and test the full workflow');
    console.log('3. Verify BEI analysis works exactly like LGD analysis');

  } catch (error) {
    console.error('❌ Error testing BEI system:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

testBEILGDPattern();
