# Multi-stage Dockerfile for prompt-evals application
# Stage 1: Build the frontend
FROM node:22.16.0-alpine AS frontend-builder

WORKDIR /app/client

# Copy frontend package files
COPY client/package*.json ./

# Install frontend dependencies
RUN npm ci

# Copy frontend source code
COPY client/ ./

# Build the frontend
RUN npm run build

# Stage 2: Setup backend and nginx
FROM node:22.16.0-alpine AS production

# Install nginx
RUN apk add --no-cache nginx

# Create nginx user and directories
RUN adduser -D -s /bin/sh nginx || true
RUN mkdir -p /var/log/nginx /var/lib/nginx/tmp /etc/nginx/conf.d

# Set working directory for backend
WORKDIR /app

# Copy backend package files
COPY server/package*.json ./server/

# Install backend dependencies
WORKDIR /app/server
RUN npm ci --only=production

# Copy backend source code
COPY server/ ./

# Copy built frontend from previous stage
COPY --from=frontend-builder /app/client/dist /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Copy environment file
COPY .env /app/.env

# Create startup script
RUN echo '#!/bin/sh' > /app/start.sh && \
    echo 'nginx &' >> /app/start.sh && \
    echo 'cd /app/server && node server.js' >> /app/start.sh && \
    chmod +x /app/start.sh

# Expose port 80 for nginx
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost/api/health || exit 1

# Start both nginx and the backend server
CMD ["/app/start.sh"]
