// Test script for BEI analysis functionality
const fs = require('fs');
const path = require('path');

async function testBEIAnalysis() {
  try {
    console.log('Testing BEI Analysis System...\n');

    // Test 1: Load BEI service
    console.log('1. Testing BEI service import...');
    const beiService = require('./server/services/beiGemini');
    console.log('✅ BEI service loaded successfully\n');

    // Test 2: Load BEI prompt
    console.log('2. Testing BEI prompt loading...');
    const promptPath = path.join(__dirname, 'server', 'data', 'bei_analyze_prompt.txt');
    const promptContent = fs.readFileSync(promptPath, 'utf8');
    console.log('✅ BEI prompt loaded successfully');
    console.log(`   Prompt length: ${promptContent.length} characters\n`);

    // Test 3: Load BEI transcript
    console.log('3. Testing BEI transcript loading...');
    const transcriptPath = path.join(__dirname, 'server', 'data', 'bei_transcript.txt');
    const transcriptContent = fs.readFileSync(transcriptPath, 'utf8');
    console.log('✅ BEI transcript loaded successfully');
    console.log(`   Transcript length: ${transcriptContent.length} characters\n`);

    // Test 4: Parse transcript
    console.log('4. Testing transcript parsing...');
    let parsedTranscript;
    try {
      parsedTranscript = JSON.parse(transcriptContent);
      console.log('✅ Transcript parsed as JSON successfully');
      console.log(`   Number of entries: ${Array.isArray(parsedTranscript) ? parsedTranscript.length : 'Not an array'}\n`);
    } catch (parseError) {
      console.log('⚠️  Transcript is not valid JSON, will be treated as plain text\n');
    }

    // Test 5: Test routes import
    console.log('5. Testing BEI routes import...');
    const beiRoutes = require('./server/routes/beiEvaluations');
    console.log('✅ BEI routes loaded successfully\n');

    console.log('🎉 All BEI system components loaded successfully!');
    console.log('\nNext steps:');
    console.log('1. Set up Supabase credentials in .env file');
    console.log('2. Run Supabase migrations');
    console.log('3. Start the server and test the full workflow');

  } catch (error) {
    console.error('❌ Error testing BEI system:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

testBEIAnalysis();
