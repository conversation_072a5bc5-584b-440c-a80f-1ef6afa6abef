# AI Prompt Chaining Manager

A web application for managing and evaluating AI prompt chains using Google's Gemini AI service.

## Features

- **Triple Evaluation Systems**:
  - **IDP Recommendation Evaluations**: Individual Development Plan recommendations based on competency gaps
  - **LGD Analysis Evaluations**: Leaderless Group Discussion analysis and participant assessment
  - **AI Interview Evaluations**: Comprehensive interview analysis with 3-step evaluation chain
- **Prompt Management**: Edit and version prompts that form evaluation chains
- **Static Flow**: Input → Prompt 1 → Gemini → Output 1 → Prompt 2 → Gemini → Final Output
- **Evaluation History**: Track all evaluation runs with timestamps and prompt versions
- **Supabase Storage**: Cloud-based storage for prompts and evaluations
- **Real-time UI**: React frontend with immediate feedback
- **Sidebar Navigation**: Easy switching between evaluation types

## Project Structure

```
prompt-evals/
├── .env                    # Environment variables (Gemini API key)
├── package.json           # Root package.json for scripts
├── server/                # Backend (Node.js + Express)
│   ├── server.js         # Main server file
│   ├── routes/           # API routes
│   ├── services/         # Gemini AI service
│   └── data/             # JSON file storage
└── client/               # Frontend (React + Vite)
    ├── src/
    │   ├── components/   # React components
    │   └── services/     # API client
    └── public/
```

## Setup Instructions

### Prerequisites
- Node.js (v16 or higher)
- Google Gemini API key

### Installation

1. **Install all dependencies:**
   ```bash
   npm run install-all
   ```

2. **Verify environment variables:**
   Make sure `.env` file contains your Gemini API key:
   ```
   GEMINI_API_KEY=your_api_key_here
   ```

3. **Start the application:**
   ```bash
   npm run dev
   ```

   This will start both the backend server (port 3001) and frontend development server (port 3000).

4. **Access the application:**
   Open your browser and go to `http://localhost:3000`

### Individual Commands

- **Start backend only:** `npm run server`
- **Start frontend only:** `npm run client`
- **Install backend deps:** `cd server && npm install`
- **Install frontend deps:** `cd client && npm install`

## Usage

1. **Edit Prompts**: Use the prompt editors to modify the two prompts in your chain
2. **Run Evaluations**: Enter input text and click "Run Prompt Chain" to execute the flow
3. **View Results**: Check the results table to see evaluation history with prompt versions

## API Endpoints

### Prompts
- `GET /api/prompts` - Get all prompts
- `GET /api/prompts/:id` - Get specific prompt
- `PUT /api/prompts/:id` - Update prompt (creates new version)

### IDP Evaluations
- `GET /api/evaluations` - Get all IDP evaluation results
- `POST /api/evaluations/run` - Run new IDP evaluation

### LGD Evaluations
- `GET /api/lgd-evaluations` - Get all LGD evaluation results
- `POST /api/lgd-evaluations/run` - Run new LGD evaluation

### AI Interview Evaluations
- `GET /api/ai-interview-evaluations` - Get all AI Interview evaluation results
- `POST /api/ai-interview-evaluations/run` - Run new AI Interview evaluation
- `GET /api/ai-interview-evaluations/:id/status` - Get evaluation status
- `PUT /api/ai-interview-evaluations/:id/annotation` - Update evaluation annotation

### Datasets
- `GET /api/datasets` - Get all datasets
- `GET /api/datasets/:id` - Get dataset by ID
- `POST /api/datasets` - Create new dataset
- `PUT /api/datasets/:id` - Update dataset
- `DELETE /api/datasets/:id` - Delete dataset

## Data Storage

- **Prompts**: Stored in Supabase `prompts` table
  - IDP prompts: IDs 1-2
  - LGD prompts: IDs 3-4
  - AI Interview prompts: IDs 5-7
- **IDP Evaluations**: Stored in Supabase `evaluations` table
- **LGD Evaluations**: Stored in Supabase `lgd_evaluations` table
- **AI Interview Evaluations**: Stored in Supabase `ai_interview_evaluations` table
- **Datasets**: Stored in Supabase `datasets` table

Database migrations are provided in the `supabase/migrations/` directory.

## AI Interview Evaluation System

The AI Interview evaluation system introduces a comprehensive 3-step evaluation chain for analyzing interview question-answer pairs:

### Features
- **Dataset Management**: Create and manage question-answer datasets with optional video URLs
- **3-Step Evaluation Chain**:
  1. **Question Evaluator** (Prompt ID 5): Analyzes each question-answer pair individually
  2. **Summary Evaluator** (Prompt ID 6): Summarizes all question evaluations
  3. **Insight Summary** (Prompt ID 7): Generates final insights and recommendations
- **Background Processing**: Evaluations run asynchronously with polling for status updates
- **Annotation Support**: Mark evaluations as "good" or "not good" for quality tracking

### Dataset Structure
Datasets contain question-answer pairs in the following JSON format:
```json
{
  "data": [
    {
      "question": "What is your name?",
      "answer": "My name is John Doe.",
      "answer_url": "https://example.com/video/1.mp4"
    }
  ]
}
```

### Evaluation Flow
1. User selects a dataset from the Dataset Manager
2. System runs Question Evaluator on each question-answer pair
3. Results are aggregated and passed to Summary Evaluator
4. Final insights are generated by Insight Summary
5. Complete results are stored with annotation support

## Technologies Used

- **Backend**: Node.js, Express, Google Generative AI SDK, Supabase
- **Frontend**: React, Vite, Axios
- **AI Service**: Google Gemini 2.5 Flash
- **Storage**: Supabase (PostgreSQL)
