const fs = require('fs').promises;
const fsSync = require('fs');
const path = require('path');
const https = require('https');
const os = require('os');
const { URL } = require('url');

class EnglishProficiencyGeminiService {
  constructor() {
    this.client = null;
    this.initPromise = this.initializeClient();
  }

  async initializeClient() {
    if (!this.client) {
      const { GoogleGenAI } = await import('@google/genai');
      this.client = new GoogleGenAI({ apiKey: process.env.GEMINI_API_KEY });
    }
    return this.client;
  }

  async generateResponse(model, prompt, systemPrompt, temperature, responseFormat = "text/plain") {
    try {
      await this.initPromise;
      const response = await this.client.models.generateContent({
        model: model,
        contents: prompt,
        config: {
          temperature: temperature,
          responseMimeType: responseFormat,
          systemInstruction: systemPrompt
        }
      });
      return response.text;
    } catch (error) {
      console.error('Error generating response:', error);
      throw error;
    }
  }

  async downloadFileFromUrl(url) {
    return new Promise((resolve, reject) => {
      try {
        const parsedUrl = new URL(url);
        const tempDir = os.tmpdir();
        const fileName = path.basename(parsedUrl.pathname) || `video_${Date.now()}.mp4`;
        const tempFilePath = path.join(tempDir, fileName);

        const file = fsSync.createWriteStream(tempFilePath);

        const request = https.get(url, (response) => {
          if (response.statusCode !== 200) {
            reject(new Error(`Failed to download file: HTTP ${response.statusCode}`));
            return;
          }

          let contentType = response.headers['content-type'];

          console.log(contentType);

          // Check if content type present if not get mime_type from file extension
          if (!contentType) {
            const ext = path.extname(fileName).toLowerCase();
            switch (ext) {
              case '.mp4':
                contentType = 'video/mp4';
                break;
              case '.mov':
                contentType = 'video/quicktime';
                break;
              case '.avi':
                contentType = 'video/x-msvideo';
                break;
              case '.wmv':
                contentType = 'video/x-ms-wmv';
                break;
              case '.webm':
                contentType = 'video/webm';
                break;
              default:
                contentType = 'video/mp4'; // Default fallback
                break;
            }
          }

          const contentLength = parseInt(response.headers['content-length'] || '0');

          response.pipe(file);

          file.on('finish', () => {
            file.close();
            resolve({
              filepath: tempFilePath,
              filename: fileName,
              filesize: contentLength,
              mime_type: contentType
            });
          });

          file.on('error', (error) => {
            fsSync.unlink(tempFilePath, () => {}); // Clean up on error
            reject(error);
          });
        });

        request.on('error', (error) => {
          reject(error);
        });

        request.setTimeout(300000); // 5 minutes timeout
        request.on('timeout', () => {
          request.destroy();
          reject(new Error('Download timeout'));
        });

      } catch (error) {
        reject(error);
      }
    });
  }

  async uploadFileToGemini(filePath, mimeType, displayName) {
    try {
      await this.initPromise;

      console.log(`Uploading file to Gemini with mimeType: ${mimeType}`);

      const uploadResponse = await this.client.files.upload({
        file: filePath,
        config: {
          mimeType: mimeType,
          displayName: displayName
        }
      });

      return {
        gemini_uri: uploadResponse.uri,
        mime_type: mimeType,
        display_name: displayName,
        name: uploadResponse.name
      };
    } catch (error) {
      console.error('Error uploading file to Gemini:', error);
      console.error('MimeType used:', mimeType);
      throw error;
    }
  }

  cleanupTempFile(filepath) {
    try {
      if (fsSync.existsSync(filepath)) {
        fsSync.unlinkSync(filepath);
        console.log(`Cleaned up temporary file: ${filepath}`);
      }
    } catch (error) {
      console.error(`Failed to cleanup temporary file ${filepath}:`, error);
    }
  }

  async checkFileStatus(fileName) {
    try {
      await this.initPromise;
      const response = await this.client.files.get({ name: fileName });
      return response.state;
    } catch (error) {
      console.error('Error checking file status:', error);
      throw error;
    }
  }

  async transcribeVideo(videoUrl, transcribePrompt, model = 'gemini-2.5-pro', temperature = 0.3) {
    let tempFilePath = null;

    try {
      console.log(`Transcribing video for English proficiency: ${videoUrl}`);

      // Download video from URL
      const downloadedFile = await this.downloadFileFromUrl(videoUrl);
      tempFilePath = downloadedFile.filepath;
      console.log(`Downloaded file: ${downloadedFile.filename} (${downloadedFile.filesize} bytes)`);

      // Read file and upload to Gemini
      console.log(`File downloaded with mime_type: ${downloadedFile.mime_type}`);

      const geminiFile = await this.uploadFileToGemini(
        tempFilePath,
        downloadedFile.mime_type,
        `english_proficiency_video_${Date.now()}`
      );
      console.log(`Uploaded to Gemini: ${geminiFile.gemini_uri}`);

      // Wait for file to be processed
      let fileStatus = await this.checkFileStatus(geminiFile.gemini_uri);
      let max_retry = 10;

      while (fileStatus !== 'ACTIVE' && max_retry > 0) {
        max_retry -= 1;
        fileStatus = await this.checkFileStatus(geminiFile.gemini_uri);
        await new Promise(resolve => setTimeout(resolve, 10000)); // Wait for 10 seconds
      }

      if (fileStatus !== 'ACTIVE') {
        throw new Error(`Failed to activate Gemini file after retries`);
      }

      // Create prompt with video file
      const prompt = [
        {
          role: 'user',
          parts: [
            {
              fileData: {
                mimeType: geminiFile.mime_type,
                fileUri: geminiFile.gemini_uri
              }
            }
          ]
        }
      ];

      const response_text = await this.generateResponse(
        model,
        prompt,
        transcribePrompt,
        temperature,
        "text/plain"
      );

      return {
        transcript: response_text.trim(),
        model: model,
        temperature: temperature
      };
    } catch (error) {
      console.error('Error transcribing video:', error);
      throw error;
    } finally {
      // Clean up temporary file
      if (tempFilePath) {
        this.cleanupTempFile(tempFilePath);
      }
    }
  }

  async analyzeCompiledTranscript(compiledTranscript, analysisPrompt, model = 'gemini-2.5-pro', temperature = 0.3) {
    try {
      console.log('Analyzing compiled transcript for English proficiency...');

      const prompt = [
        {
          role: 'user',
          parts: [{ text: compiledTranscript }]
        }
      ];

      const response = await this.generateResponse(
        model,
        prompt,
        analysisPrompt,
        temperature,
        "application/json"
      );

      // Parse JSON response
      let analysisResult;
      try {
        analysisResult = JSON.parse(response);
      } catch (parseError) {
        console.error('Error parsing analysis JSON:', parseError);
        throw new Error('Failed to parse analysis response as JSON');
      }

      return {
        analysis: analysisResult,
        model: model,
        temperature: temperature
      };
    } catch (error) {
      console.error('Error analyzing compiled transcript:', error);
      throw error;
    }
  }

  async runEnglishProficiencyPromptChain(
    dataset,
    transcribePrompt,
    analysisPrompt,
    transcribeModel = 'gemini-2.5-flash',
    transcribeTemperature = 0,
    analysisModel = 'gemini-2.5-pro',
    analysisTemperature = 0.4
  ) {
    try {
      console.log('Starting English Proficiency evaluation chain...');

      const transcriptions = [];

      // Ensure we have the correct dataset structure
      const questionAnswerPairs = dataset.data && dataset.data.data ? dataset.data.data : [];

      if (questionAnswerPairs.length === 0) {
        console.warn('No question-answer pairs found in dataset');
      }

      console.log(questionAnswerPairs);

      // Step 1: Transcribe each video
      console.log(`Step 1: Transcribing ${questionAnswerPairs.length} videos...`);
      for (let i = 0; i < questionAnswerPairs.length; i++) {
        const pair = questionAnswerPairs[i];
        console.log(`Transcribing video ${i + 1}/${questionAnswerPairs.length}: ${pair.question}`);

        if (!pair.answer_url) {
          console.error(`Missing answer_url for question ${i + 1}`);
          transcriptions.push({
            question: pair.question,
            transcript: 'Error: Missing video URL',
            model: transcribeModel,
            temperature: transcribeTemperature,
            error: 'Missing answer_url in dataset'
          });
          continue;
        }

        try {
          const transcriptionResult = await this.transcribeVideo(
            pair.answer_url,
            transcribePrompt,
            transcribeModel,
            transcribeTemperature
          );

          transcriptions.push({
            question: pair.question,
            transcript: transcriptionResult.transcript,
            model: transcriptionResult.model,
            temperature: transcriptionResult.temperature
          });
        } catch (error) {
          console.error(`Error transcribing video ${i + 1}:`, error);
          transcriptions.push({
            question: pair.question,
            transcript: 'Error: Failed to transcribe video',
            model: transcribeModel,
            temperature: transcribeTemperature,
            error: error.message
          });
        }
      }

      // Step 2: Compile transcripts into structured format
      console.log('Step 2: Compiling transcripts...');
      let compiledTranscript = '';
      transcriptions.forEach((item, index) => {
        compiledTranscript += `Question ${index + 1}: ${item.question}\n`;
        compiledTranscript += `Answer Transcript: ${item.transcript}\n\n`;
      });

      // Step 3: Analyze compiled transcript for English proficiency
      console.log('Step 3: Analyzing compiled transcript for English proficiency...');
      const analysisResult = await this.analyzeCompiledTranscript(
        compiledTranscript,
        analysisPrompt,
        analysisModel,
        analysisTemperature
      );

      // Combine all results
      const finalResult = {
        transcriptions,
        compiledTranscript,
        analysis: analysisResult.analysis,
        finalOutput: {
          evaluations: analysisResult.analysis,
          transcriptions: transcriptions,
          compiledTranscript: compiledTranscript
        },
        models: {
          transcribe: transcribeModel,
          analysis: analysisModel
        },
        temperatures: {
          transcribe: transcribeTemperature,
          analysis: analysisTemperature
        }
      };

      console.log('English Proficiency evaluation chain completed successfully');
      return finalResult;
    } catch (error) {
      console.error('Error in English Proficiency prompt chain:', error);
      throw error;
    }
  }
}

module.exports = new EnglishProficiencyGeminiService();
