const https = require('https');
const { URL } = require('url');
const fs = require('fs');

class EtrayGeminiService {
  constructor() {
    this.client = null;
    this.initPromise = this.initializeClient();
  }

  async initializeClient() {
    if (!this.client) {
      const { GoogleGenAI } = await import('@google/genai');
      this.client = new GoogleGenAI({
        apiKey: process.env.GEMINI_API_KEY,
        httpOptions: {
          timeout: 600000
        }
      });
    }
    return this.client;
  }



  async generateResponseWithInlineData(model, prompt, config, inlineDataFiles = []) {
    return new Promise((resolve, reject) => {
      try {
        const requestUrl = `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent`;
        const queryParams = `key=${process.env.GEMINI_API_KEY}`;
        const url = new URL(`${requestUrl}?${queryParams}`);

        // Build content parts - text prompt and inline data
        const contentParts = [{ text: prompt }];

        // Add inline data files
        inlineDataFiles.forEach(fileObject => {
          const base64Data = fileObject.data;
          const mimeType = fileObject.mime_type;
          contentParts.push({
            inline_data: {
              mime_type: mimeType,
              data: base64Data
            }
          });
        });

        const postData = JSON.stringify({
          system_instruction: { parts: [{ text: config.systemInstruction }] },
          contents: [{ parts: contentParts }],
          generationConfig: {
            temperature: config.temperature,
            responseMimeType: config.responseMimeType
          }
        });

        // console.log('postData:', postData);

        const options = {
          hostname: url.hostname,
          port: url.port || 443,
          path: url.pathname + url.search,
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Content-Length': Buffer.byteLength(postData)
          },
          timeout: 600000 // 10 minutes timeout
        };

        const req = https.request(options, (res) => {
          let data = '';
          res.on('data', (chunk) => { data += chunk; });

          res.on('end', () => {
            try {
              if (res.statusCode !== 200) {
                console.error('Generation error response:', data);
                reject(new Error(`HTTP error! status: ${res.statusCode}, response: ${data}`));
                return;
              }

              const responseData = JSON.parse(data);
              resolve(responseData.candidates[0].content.parts[0].text);
            } catch (parseError) {
              console.error('Error parsing response:', parseError);
              reject(new Error('Failed to parse response from Gemini'));
            }
          });
        });

        req.on('error', (error) => {
          console.error('Request error:', error);
          reject(new Error('Failed to generate response from Gemini'));
        });

        req.on('timeout', () => {
          req.destroy();
          reject(new Error('Request timeout - Gemini API took too long to respond'));
        });

        req.setTimeout(600000); // 10 minutes timeout
        req.write(postData);
        req.end();

      } catch (error) {
        console.error('Error generating response:', error);
        reject(new Error('Failed to generate response from Gemini'));
      }
    });
  }

  async generateResponse(model, prompt, config) {
    return this.generateResponseWithInlineData(model, prompt, config, []);
  }

  // Helper function to read file content as base64 for inline data
  async readFileAsBase64(filePath) {
    return new Promise((resolve, reject) => {
      fs.readFile(filePath, (err, data) => {
        if (err) {
          reject(err);
        } else {
          resolve(data.toString('base64'));
        }
      });
    });
  }

  // Helper function to get proper MIME type for inline data
  getMimeTypeForInlineData(file) {
    // Use the detected mimetype from multer
    if (file.mimetype) {
      return file.mimetype;
    }

    // Fallback based on file extension
    const path = require('path');
    const ext = path.extname(file.originalname || file.filename || '').toLowerCase();

    switch (ext) {
      case '.pdf':
        return 'application/pdf';
      case '.txt':
        return 'text/plain';
      case '.csv':
        return 'text/csv';
      default:
        return 'application/octet-stream';
    }
  }

  async runEtrayPromptChain(input, analysisPrompt, formattingPrompt) {
    try {
      await this.initPromise; // Ensure client is initialized

      const { transcript, competencies, workingResultFile, answerKeyFile } = input;

      // Read files as base64 for inline data if provided
      let workingResultData = null;
      let answerKeyData = null;

      if (workingResultFile && workingResultFile.path) {
        console.log('Reading working result file for inline data...');
        console.log('Working result file details:', {
          originalname: workingResultFile.originalname,
          filename: workingResultFile.filename,
          mimetype: workingResultFile.mimetype,
          path: workingResultFile.path
        });

        const workingResultMimeType = this.getMimeTypeForInlineData(workingResultFile);
        console.log('Working result file MIME type:', workingResultMimeType);

        const workingResultBase64 = await this.readFileAsBase64(workingResultFile.path);
        workingResultData = {
          mime_type: workingResultMimeType,
          data: workingResultBase64
        };
        console.log('Working result file read successfully for inline data');
      }

      if (answerKeyFile && answerKeyFile.path) {
        console.log('Reading answer key file for inline data...');
        console.log('Answer key file details:', {
          originalname: answerKeyFile.originalname,
          filename: answerKeyFile.filename,
          mimetype: answerKeyFile.mimetype,
          path: answerKeyFile.path
        });

        const answerKeyMimeType = this.getMimeTypeForInlineData(answerKeyFile);
        console.log('Answer key file MIME type:', answerKeyMimeType);

        const answerKeyBase64 = await this.readFileAsBase64(answerKeyFile.path);
        answerKeyData = {
          mime_type: answerKeyMimeType,
          data: answerKeyBase64
        };
        console.log('Answer key file read successfully for inline data');
      }

      // Step 1: Run E-tray analysis prompt with transcript, competencies, and files
      const analysisSystemPrompt = analysisPrompt.replace(
        '{{ assessment_guidelines }}',
        competencies
      );

      const analysisUserPrompt = `
        Here is the E-tray simulation data to analyze:
        
        Transcript: ${transcript}
        
        ${workingResultFile ? 'Working Result Document is attached as a file.' : 'No working result document provided.'}
        ${answerKeyFile ? 'Answer Key Document is attached as a file.' : 'No answer key document provided.'}
        
        Please analyze the participant's performance based on the provided documents and assessment guidelines.
      `;

      const analysisConfig = {
        temperature: 0.2,
        responseMimeType: "text/plain",
        systemInstruction: analysisSystemPrompt
      };

      console.log(`Current time: ${new Date().toISOString()}`);
      console.log('Running E-tray analysis prompt...');

      // Collect inline data files for analysis
      const inlineDataFiles = [];
      if (workingResultData) {
        inlineDataFiles.push(workingResultData);
      }
      if (answerKeyData) {
        inlineDataFiles.push(answerKeyData);
      }

      console.log('Inline data files for analysis:', inlineDataFiles.length, 'files');
      console.log('Working Result Data:', workingResultData ? 'Present' : 'Not provided');
      console.log('Answer Key Data:', answerKeyData ? 'Present' : 'Not provided');

      const analysisOutput = await this.generateResponseWithInlineData(
        'gemini-2.5-pro',
        analysisUserPrompt,
        analysisConfig,
        inlineDataFiles
      );

      console.log(`Current time: ${new Date().toISOString()}`);
      console.log('E-tray analysis completed. Starting formatting...');

      // Step 2: Run E-tray formatting prompt to convert analysis to structured format
      const formattingUserPrompt = `
        Please convert the following E-tray analysis to structured JSON format:

        ${analysisOutput}
      `;

      const formattingConfig = {
        temperature: 0.1,
        responseMimeType: "application/json",
        systemInstruction: formattingPrompt
      };

      const formattingOutput = await this.generateResponse(
        'gemini-2.5-flash',
        formattingUserPrompt,
        formattingConfig
      );

      console.log(`Current time: ${new Date().toISOString()}`);
      console.log('E-tray formatting completed.');

      return {
        analysis: {
          prompt: analysisUserPrompt,
          systemInstruction: analysisSystemPrompt,
          output: analysisOutput,
          inlineDataFiles: inlineDataFiles
        },
        formatting: {
          prompt: formattingUserPrompt,
          systemInstruction: formattingPrompt,
          output: formattingOutput
        },
        finalOutput: formattingOutput
      };
    } catch (error) {
      console.error('Error in E-tray prompt chain:', error);
      throw error;
    }
  }
}

module.exports = new EtrayGeminiService();
