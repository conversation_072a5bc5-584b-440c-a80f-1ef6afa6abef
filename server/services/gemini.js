class GeminiService {
  constructor() {
    this.client = null;
    this.initPromise = this.initializeClient();
  }

  async initializeClient() {
    if (!this.client) {
      const { GoogleGenAI } = await import('@google/genai');
      this.client = new GoogleGenAI({ apiKey: process.env.GEMINI_API_KEY });
    }
    return this.client;
  }

  async generateResponse(prompt) {
    try {
      await this.initPromise; // Ensure client is initialized
      const response = await this.client.models.generateContent({
        model: 'gemini-2.5-flash-preview-05-20',
        contents: prompt,
        config: {
          temperature: 0.2,
          responseMimeType: "application/json"
        }
      });
      return response.text;
    } catch (error) {
      console.error('Error generating response:', error);
      throw new Error('Failed to generate response from <PERSON>');
    }
  }

  async runPromptChain(input, prompt1, prompt2) {
    try {
      await this.initPromise; // Ensure client is initialized
      let fullPrompt1, fullPrompt2;

      // Handle new structured input format
      if (typeof input === 'object' && input.roleName && input.competencyGaps) {
        const { roleName, competencyGaps } = input;

        // Step 1: Generate objectives for each competency gap
        const objectivePromises = competencyGaps.map(async (gap) => {
          const gapPrompt = prompt1.replace('#{competency_gap.to_json}', JSON.stringify(gap));
          const result = await this.generateResponse(gapPrompt);
          try {
            const parsed = JSON.parse(result);
            return parsed.objective_statement || '';
          } catch (error) {
            console.error('Error parsing objective response:', error);
            return '';
          }
        });

        const generatedObjectives = await Promise.all(objectivePromises);
        const validObjectives = generatedObjectives.filter(obj => obj.trim());

        console.log(JSON.stringify(validObjectives))

        // Step 2: Run second prompt with all data (only generated objectives)
        fullPrompt2 = prompt2
          .replace('#{role_name.to_json}', JSON.stringify(roleName))
          .replace('#{competency_gaps.to_json}', JSON.stringify(competencyGaps))
          .replace('#{objectives.to_json}', JSON.stringify(validObjectives));

        const finalOutput = await this.generateResponse(fullPrompt2);

        return {
          step1: {
            prompt: `Generated objectives for ${competencyGaps.length} competency gaps`,
            output: `Generated objectives: ${JSON.stringify(validObjectives)}`
          },
          step2: {
            prompt: fullPrompt2,
            output: finalOutput
          },
          finalOutput,
          objectives: validObjectives
        };
      } else {
        // Handle legacy format for backward compatibility
        let formattedInput;
        if (Array.isArray(input)) {
          // If input is an array of competency gaps, format as a list
          formattedInput = input.join('\n');
        } else {
          // If input is a string, use as is
          formattedInput = input;
        }

        // Step 1: Run first prompt with input
        fullPrompt1 = `${prompt1}\n\nInput: ${formattedInput}`;
        const output1 = await this.generateResponse(fullPrompt1);

        // Step 2: Run second prompt with output from step 1
        fullPrompt2 = `${prompt2}\n\nInput: ${output1}`;
        const finalOutput = await this.generateResponse(fullPrompt2);

        return {
          step1: {
            prompt: fullPrompt1,
            output: output1
          },
          step2: {
            prompt: fullPrompt2,
            output: finalOutput
          },
          finalOutput
        };
      }
    } catch (error) {
      console.error('Error in prompt chain:', error);
      throw error;
    }
  }
}

module.exports = new GeminiService();
