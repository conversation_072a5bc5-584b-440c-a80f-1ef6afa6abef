const https = require('https');
const fs = require('fs');
const path = require('path');
const os = require('os');
const { URL } = require('url');

class AIInterviewGeminiService {
  constructor() {
    this.client = null;
    this.initPromise = this.initializeClient();
  }

  async initializeClient() {
    if (!this.client) {
      const { GoogleGenAI } = await import('@google/genai');
      this.client = new GoogleGenAI({ apiKey: process.env.GEMINI_API_KEY });
    }
    return this.client;
  }

  async generateResponse(model, prompt, systemPrompt, temperature, responseFormat = "application/json") {
    try {
      await this.initPromise;
      const response = await this.client.models.generateContent({
        model: model,
        contents: prompt,
        config: {
          temperature: temperature,
          responseMimeType: responseFormat,
          systemInstruction: systemPrompt
        }
      });
      return response.text;
    } catch (error) {
      console.error('Error generating response:', error);
      throw error;
    }
  }

  async downloadFileFromUrl(url) {
    return new Promise((resolve, reject) => {
      try {
        const parsedUrl = new URL(url);
        const tempDir = os.tmpdir();
        const fileName = path.basename(parsedUrl.pathname) || `video_${Date.now()}.mp4`;
        const tempFilePath = path.join(tempDir, fileName);

        const file = fs.createWriteStream(tempFilePath);

        const request = https.get(url, (response) => {
          if (response.statusCode !== 200) {
            reject(new Error(`Failed to download file: HTTP ${response.statusCode}`));
            return;
          }

          const contentType = response.headers['content-type'] || 'video/mp4';
          const contentLength = parseInt(response.headers['content-length'] || '0');

          response.pipe(file);

          file.on('finish', () => {
            file.close();
            resolve({
              filepath: tempFilePath,
              filename: fileName,
              filesize: contentLength,
              mime_type: contentType
            });
          });

          file.on('error', (error) => {
            fs.unlink(tempFilePath, () => {}); // Clean up on error
            reject(error);
          });
        });

        request.on('error', (error) => {
          reject(error);
        });

        request.setTimeout(300000); // 5 minutes timeout
        request.on('timeout', () => {
          request.destroy();
          reject(new Error('Download timeout'));
        });

      } catch (error) {
        reject(error);
      }
    });
  }

  async generateUrlUpload(params) {
    const { filename, filesize, mime_type } = params;

    return new Promise((resolve, reject) => {
      const requestUrl = 'https://generativelanguage.googleapis.com/upload/v1beta/files';
      const queryParams = `key=${process.env.GEMINI_API_KEY}`;
      const url = new URL(`${requestUrl}?${queryParams}`);

      const postData = JSON.stringify({
        file: { display_name: filename }
      });

      const options = {
        hostname: url.hostname,
        port: url.port || 443,
        path: url.pathname + url.search,
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Goog-Upload-Protocol': 'resumable',
          'X-Goog-Upload-Command': 'start',
          'X-Goog-Upload-Header-Content-Length': filesize.toString(),
          'X-Goog-Upload-Header-Content-Type': mime_type
        }
      };

      const req = https.request(options, (res) => {
        if (res.statusCode !== 200) {
          reject(new Error(`Failed to generate upload URL: HTTP ${res.statusCode}`));
          return;
        }

        const uploadUrl = res.headers['x-goog-upload-url'];
        if (!uploadUrl) {
          reject(new Error('No upload URL returned from Gemini'));
          return;
        }

        resolve(uploadUrl);
      });

      req.on('error', (error) => {
        reject(error);
      });

      req.write(postData);
      req.end();
    });
  }

  async uploadFileToGemini(params) {
    const { filepath, filesize, filename, mime_type } = params;

    try {
      // Step 1: Generate upload URL
      const uploadUrl = await this.generateUrlUpload({ filename, filesize, mime_type });

      // Step 2: Upload the file
      return new Promise((resolve, reject) => {
        const parsedUrl = new URL(uploadUrl);
        const fileData = fs.readFileSync(filepath);

        const options = {
          hostname: parsedUrl.hostname,
          port: parsedUrl.port || 443,
          path: parsedUrl.pathname + parsedUrl.search,
          method: 'PUT',
          headers: {
            'Content-Length': filesize.toString(),
            'X-Goog-Upload-Command': 'upload, finalize',
            'X-Goog-Upload-Offset': '0'
          }
        };

        const req = https.request(options, (res) => {
          let data = '';
          res.on('data', (chunk) => { data += chunk; });

          res.on('end', () => {
            try {
              if (res.statusCode !== 200) {
                reject(new Error(`Failed to upload file: HTTP ${res.statusCode}`));
                return;
              }

              const responseData = JSON.parse(data);
              if (responseData.file) {
                resolve({
                  mime_type: responseData.file.mimeType,
                  gemini_uri: responseData.file.uri
                });
              } else {
                reject(new Error('Invalid response from Gemini file upload'));
              }
            } catch (parseError) {
              reject(new Error('Failed to parse Gemini upload response'));
            }
          });
        });

        req.on('error', (error) => {
          reject(error);
        });

        req.write(fileData);
        req.end();
      });
    } catch (error) {
      throw new Error(`Failed to upload file to Gemini: ${error.message}`);
    }
  }

  async checkFileStatus(geminiUri) {
    const queryParams = `key=${process.env.GEMINI_API_KEY}`;
    const url = new URL(`${geminiUri}?${queryParams}`);

    return new Promise((resolve, reject) => {
      const options = {
        hostname: url.hostname,
        port: url.port || 443,
        path: url.pathname + url.search,
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      };

      const req = https.request(options, (res) => {
        let data = '';
        res.on('data', (chunk) => { data += chunk; });

        res.on('end', () => {
          try {
            if (res.statusCode !== 200) {
              reject(new Error(`Failed to check file status: HTTP ${res.statusCode}`));
              return;
            }

            const responseData = JSON.parse(data);
            resolve(responseData.state);
          } catch (parseError) {
            reject(new Error('Failed to parse Gemini file status response'));
          }
        });
      });

      req.on('error', (error) => {
        reject(error);
      });

      req.end();
    });
  }

  cleanupTempFile(filepath) {
    try {
      if (fs.existsSync(filepath)) {
        fs.unlinkSync(filepath);
        console.log(`Cleaned up temporary file: ${filepath}`);
      }
    } catch (error) {
      console.error(`Failed to cleanup temporary file ${filepath}:`, error);
    }
  }

  async runQuestionEvaluator(questionAnswerPair, prompt1Content) {
    let tempFilePath = null;

    try {
      const userPrompt = `
        Here is my answer of the following question:
        ${questionAnswerPair.question}
      `;

      // Initialize prompt parts with text
      const userParts = [{ text: userPrompt }];

      // Handle video file upload if answer_url exists
      if (questionAnswerPair.answer_url) {
        console.log(`Downloading video from: ${questionAnswerPair.answer_url}`);

        // Download file from S3
        const downloadedFile = await this.downloadFileFromUrl(questionAnswerPair.answer_url);
        tempFilePath = downloadedFile.filepath;
        console.log(`Downloaded file: ${downloadedFile.filename} (${downloadedFile.filesize} bytes)`);

        // Upload to Gemini
        const geminiFile = await this.uploadFileToGemini(downloadedFile);
        console.log(`Uploaded to Gemini: ${geminiFile.gemini_uri}`);

        // Loop to check file status until active
        let fileStatus = 'PENDING';
        let max_retry = 10

        while (fileStatus !== 'ACTIVE' && max_retry > 0) {
          max_retry -= 1;
          fileStatus = await this.checkFileStatus(geminiFile.gemini_uri);

          await new Promise(resolve => setTimeout(resolve, 10000)); // Wait for 10 seconds
        }

        if (fileStatus !== 'ACTIVE') {
          throw new Error(`Failed to activate Gemini file after ${max_retry} retries`);
        }

        // Add file data to prompt parts
        userParts.push({
          fileData: {
            mimeType: geminiFile.mime_type,
            fileUri: geminiFile.gemini_uri
          }
        });
      }

      const prompt = [
        {
          role: 'user',
          parts: userParts
        }
      ];

      const response = await this.generateResponse(
        'gemini-2.5-pro-preview-06-05',
        prompt,
        prompt1Content,
        0.3
      );

      try {
        const parsed = JSON.parse(response);
        return {
          questionAnswerPair,
          evaluation: parsed,
          rawResponse: response
        };
      } catch (error) {
        console.error('Error parsing question evaluator response:', error);
        throw new Error('Failed to parse question evaluator response');
      }
    } catch (error) {
      console.error('Error in runQuestionEvaluator:', error);
      throw error;
    } finally {
      // Clean up temporary file
      if (tempFilePath) {
        this.cleanupTempFile(tempFilePath);
      }
    }
  }

  async runSummaryEvaluator(questionEvaluations, prompt2Content) {
    // Aggregate all evaluations
    const aggregatedData = {
      evaluations: {},
      video_summaries: []
    };

    // Process each question evaluation
    questionEvaluations.forEach((qe) => {
      if (qe.evaluation && qe.evaluation.evaluations) {
        Object.keys(qe.evaluation.evaluations).forEach(criteria => {
          if (!aggregatedData.evaluations[criteria]) {
            aggregatedData.evaluations[criteria] = {
              analysis: [],
              scores: []
            };
          }
          
          const evaluation = qe.evaluation.evaluations[criteria];
          if (evaluation.analysis) {
            aggregatedData.evaluations[criteria].analysis.push(evaluation.analysis);
          }
          if (evaluation.score !== null && evaluation.score !== undefined) {
            aggregatedData.evaluations[criteria].scores.push(evaluation.score);
          }
        });
      }

      // Add transcript as video summary
      if (qe.evaluation && qe.evaluation.transcript) {
        aggregatedData.video_summaries.push(qe.evaluation.transcript);
      }
    });

    // Calculate average scores
    Object.keys(aggregatedData.evaluations).forEach(criteria => {
      const scores = aggregatedData.evaluations[criteria].scores;
      if (scores.length > 0) {
        const average = scores.reduce((sum, score) => sum + score, 0) / scores.length;
        aggregatedData.evaluations[criteria].score = average;
      }
    });

    const userPrompt = `Here is the data of the interview:
${JSON.stringify(aggregatedData)}`;

    const prompt = [
      {
        role: 'user',
        parts: [{ text: userPrompt }]
      }
    ];

    const response = await this.generateResponse(
      'gemini-2.5-flash-preview-05-20',
      prompt,
      prompt2Content,
      0.3
    );
    
    try {
      const parsed = JSON.parse(response);
      return {
        summaryEvaluation: parsed,
        aggregatedData,
        rawResponse: response
      };
    } catch (error) {
      console.error('Error parsing summary evaluator response:', error);
      throw new Error('Failed to parse summary evaluator response');
    }
  }

  async runInsightSummary(summaryEvaluation, prompt3Content) {
    // Transform summary evaluation to the expected format for insight summary
    const evaluationData = {};
    
    Object.keys(summaryEvaluation).forEach(criteria => {
      if (criteria !== 'list_analysis' && criteria !== 'summary') {
        evaluationData[criteria] = {
          scores: 0, // We'll need to calculate this from the original data
          analysis: summaryEvaluation[criteria]
        };
      }
    });

    const userPrompt = `Here the candidate summary data
${JSON.stringify(evaluationData)}`;

    const prompt = [
      {
        role: 'user',
        parts: [{ text: userPrompt }]
      }
    ];

    const response = await this.generateResponse(
      'gemini-2.5-flash-preview-05-20',
      prompt,
      prompt3Content,
      1,
      "text/plain"
    );
    
    return {
      insightSummary: response,
      rawResponse: response
    };
  }

  async runAIInterviewPromptChain(dataset, prompt1Content, prompt2Content, prompt3Content) {
    try {
      console.log('Starting AI Interview evaluation chain...');
      
      // Step 1: Run question evaluator for each question-answer pair
      console.log('Step 1: Running question evaluators...');
      const questionEvaluations = [];
      
      for (const questionAnswerPair of dataset.data) {
        try {
          const evaluation = await this.runQuestionEvaluator(questionAnswerPair, prompt1Content);
          questionEvaluations.push(evaluation);
          console.log(`Evaluated question: ${questionAnswerPair.question.substring(0, 50)}...`);
        } catch (error) {
          console.error('Error evaluating question:', error);
          // Continue with other questions even if one fails
          questionEvaluations.push({
            questionAnswerPair,
            evaluation: null,
            error: error.message
          });
        }
      }

      // Step 2: Run summary evaluator
      console.log('Step 2: Running summary evaluator...');
      const summaryResult = await this.runSummaryEvaluator(questionEvaluations, prompt2Content);

      // Step 3: Run insight summary
      console.log('Step 3: Running insight summary...');
      const insightResult = await this.runInsightSummary(summaryResult.summaryEvaluation, prompt3Content);

      // Combine all results
      const finalResult = {
        questionEvaluations,
        summaryEvaluation: summaryResult.summaryEvaluation,
        aggregatedData: summaryResult.aggregatedData,
        insightSummary: insightResult.insightSummary,
        finalOutput: {
          evaluations: summaryResult.summaryEvaluation,
          insight: insightResult.insightSummary,
          details: questionEvaluations
        }
      };

      console.log('AI Interview evaluation chain completed successfully');
      return finalResult;

    } catch (error) {
      console.error('Error in AI Interview prompt chain:', error);
      throw error;
    }
  }
}

module.exports = new AIInterviewGeminiService();
