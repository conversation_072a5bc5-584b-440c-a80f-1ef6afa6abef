const https = require('https');
const { URL } = require('url');

class BEIGeminiService {
  constructor() {
    this.client = null;
    this.initPromise = this.initializeClient();
  }

  async initializeClient() {
    if (!this.client) {
      const { GoogleGenAI } = await import('@google/genai');
      this.client = new GoogleGenAI({
        apiKey: process.env.GEMINI_API_KEY,
        httpOptions: {
          timeout: 600000
        }
      });
    }
    return this.client;
  }

  async generateResponse(model, prompt, config) {
    return new Promise((resolve, reject) => {
      try {
        const requestUrl = `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent`;
        const queryParams = `key=${process.env.GEMINI_API_KEY}`;
        const url = new URL(`${requestUrl}?${queryParams}`);

        const postData = JSON.stringify({
          system_instruction: { parts: [{ text: config.systemInstruction }] },
          contents: [{ parts: [{ text: prompt }] }],
          generationConfig: {
            temperature: config.temperature,
            responseMimeType: config.responseMimeType
          }
        });

        const options = {
          hostname: url.hostname,
          port: url.port || 443,
          path: url.pathname + url.search,
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Content-Length': Buffer.byteLength(postData)
          },
          timeout: 600000 // 10 minutes timeout
        };

        const req = https.request(options, (res) => {
          let data = '';
          res.on('data', (chunk) => { data += chunk; });

          res.on('end', () => {
            try {
              if (res.statusCode !== 200) {
                reject(new Error(`HTTP error! status: ${res.statusCode}`));
                return;
              }

              const responseData = JSON.parse(data);
              resolve(responseData.candidates[0].content.parts[0].text);
            } catch (parseError) {
              console.error('Error parsing response:', parseError);
              reject(new Error('Failed to parse response from Gemini'));
            }
          });
        });

        req.on('error', (error) => {
          console.error('Request error:', error);
          reject(new Error('Failed to generate response from Gemini'));
        });

        req.on('timeout', () => {
          req.destroy();
          reject(new Error('Request timeout - Gemini API took too long to respond'));
        });

        req.setTimeout(600000); // 10 minutes timeout
        req.write(postData);
        req.end();

      } catch (error) {
        console.error('Error generating response:', error);
        reject(new Error('Failed to generate response from Gemini'));
      }
    });
  }

  async runBEIPromptChain(input, analysisPrompt, formattingPrompt) {
    try {
      await this.initPromise; // Ensure client is initialized

      const { transcript, competencies } = input;

      // Step 1: Run BEI analysis prompt with transcript and competencies
      const analysisSystemPrompt = analysisPrompt.replace(
        '{{ bei_competencies }}',
        competencies
      )

      const analysisUserPrompt = `
        Here is the interview transcript to analyze:
        ${transcript}
      `

      const analysisConfig = {
        temperature: 0.2,
        responseMimeType: "text/plain",
        systemInstruction: analysisSystemPrompt
      };

      console.log(`Current time: ${new Date().toISOString()}`)
      console.log('Running BEI analysis prompt...');

      const analysisOutput = await this.generateResponse(
        'gemini-2.5-pro',
        analysisUserPrompt,
        analysisConfig
      );

      console.log(`Current time: ${new Date().toISOString()}`)
      console.log('BEI analysis completed. Starting formatting...');

      // Step 2: Run BEI formatting prompt to convert analysis to structured format
      const formattingUserPrompt = `
        Please convert the following BEI analysis to structured JSON format:

        ${analysisOutput}
      `

      const formattingConfig = {
        temperature: 0.1,
        responseMimeType: "application/json",
        systemInstruction: formattingPrompt
      };

      const formattingOutput = await this.generateResponse(
        'gemini-2.5-flash',
        formattingUserPrompt,
        formattingConfig
      );

      console.log(`Current time: ${new Date().toISOString()}`)
      console.log('BEI formatting completed.');

      return {
        analysis: {
          prompt: analysisUserPrompt,
          systemInstruction: analysisSystemPrompt,
          output: analysisOutput
        },
        formatting: {
          prompt: formattingUserPrompt,
          systemInstruction: formattingPrompt,
          output: formattingOutput
        },
        finalOutput: formattingOutput
      };
    } catch (error) {
      console.error('Error in BEI prompt chain:', error);
      throw error;
    }
  }
}

module.exports = new BEIGeminiService();
