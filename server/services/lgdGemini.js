const https = require('https');
const { URL } = require('url');

class LGDGeminiService {
  constructor() {
    this.client = null;
    this.initPromise = this.initializeClient();
  }

  async initializeClient() {
    if (!this.client) {
      const { GoogleGenAI } = await import('@google/genai');
      this.client = new GoogleGenAI({
        apiKey: process.env.GEMINI_API_KEY,
        httpOptions: {
          timeout: 600000
        }
      });
    }
    return this.client;
  }

  async generateResponse(model, prompt, config) {
    return new Promise((resolve, reject) => {
      try {
        const requestUrl = `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent`;
        const queryParams = `key=${process.env.GEMINI_API_KEY}`;
        const url = new URL(`${requestUrl}?${queryParams}`);

        const postData = JSON.stringify({
          system_instruction: { parts: [{ text: config.systemInstruction }] },
          contents: [{ parts: [{ text: prompt }] }],
          generationConfig: {
            temperature: config.temperature,
            responseMimeType: config.responseMimeType
          }
        });

        const options = {
          hostname: url.hostname,
          port: url.port || 443,
          path: url.pathname + url.search,
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Content-Length': Buffer.byteLength(postData)
          },
          timeout: 600000 // 10 minutes timeout
        };

        const req = https.request(options, (res) => {
          let data = '';
          res.on('data', (chunk) => { data += chunk; });

          res.on('end', () => {
            try {
              if (res.statusCode !== 200) {
                reject(new Error(`HTTP error! status: ${res.statusCode}`));
                return;
              }

              const responseData = JSON.parse(data);
              resolve(responseData.candidates[0].content.parts[0].text);
            } catch (parseError) {
              console.error('Error parsing response:', parseError);
              reject(new Error('Failed to parse response from Gemini'));
            }
          });
        });

        req.on('error', (error) => {
          console.error('Request error:', error);
          reject(new Error('Failed to generate response from Gemini'));
        });

        req.on('timeout', () => {
          req.destroy();
          reject(new Error('Request timeout - Gemini API took too long to respond'));
        });

        req.setTimeout(600000); // 10 minutes timeout
        req.write(postData);
        req.end();

      } catch (error) {
        console.error('Error generating response:', error);
        reject(new Error('Failed to generate response from Gemini'));
      }
    });
  }

  async runLGDPromptChain(input, analysisPrompt, formattingPrompt) {
    try {
      await this.initPromise; // Ensure client is initialized

      const { transcript, competencies } = input;

      // Step 1: Run LGD analysis prompt with transcript and competencies
      const analysisSystemPrompt = analysisPrompt.replace(
        '{{lgd_competencies}}',
        competencies
      )

      const analysisUserPrompt = `
        Here is the transcripts:
        ${transcript}
      `

      const analysisConfig = {
        temperature: 0.2,
        responseMimeType: "text/plain",
        systemInstruction: analysisSystemPrompt
      };

      console.log(`Current time: ${new Date().toISOString()}`)
      console.log('Running LGD analysis prompt...');

      const analysisOutput = await this.generateResponse(
        'gemini-2.5-pro-preview-06-05',
        analysisUserPrompt,
        analysisConfig
      );

      // Step 2: Run formatting prompt with analysis output
      const formattingUserPrompt = `
        You need to format this text content to JSON format
        ${analysisOutput}
      `

      const formattingConfig = {
        temperature: 0,
        responseMimeType: "application/json",
        systemInstruction: formattingPrompt
      };

      console.log(`Current time: ${new Date().toISOString()}`)
      console.log('Running LGD formatting prompt...');

      const finalOutput = await this.generateResponse(
        'gemini-2.5-flash-preview-05-20',
        formattingUserPrompt,
        formattingConfig
      );

      console.log(`Current time: ${new Date().toISOString()}`)
      console.log('LGD prompt chain completed.');

      return {
        step1: {
          prompt: analysisUserPrompt,
          systemInstruction: analysisSystemPrompt,
          output: analysisOutput
        },
        step2: {
          prompt: formattingUserPrompt,
          systemInstruction: formattingPrompt,
          output: finalOutput
        },
        finalOutput
      };
    } catch (error) {
      console.error('Error in LGD prompt chain:', error);
      throw error;
    }
  }
}

module.exports = new LGDGeminiService();
