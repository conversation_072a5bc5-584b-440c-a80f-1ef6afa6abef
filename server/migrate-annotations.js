const supabase = require('./supabaseClient');

async function addAnnotationColumns() {
  try {
    console.log('Adding annotation column to evaluations table...');
    
    // Add annotation column to evaluations table
    const { error: evalError } = await supabase.rpc('exec_sql', {
      sql: 'ALTER TABLE evaluations ADD COLUMN IF NOT EXISTS annotation TEXT;'
    });
    
    if (evalError) {
      console.error('Error adding annotation column to evaluations:', evalError);
    } else {
      console.log('✓ Added annotation column to evaluations table');
    }

    console.log('Adding annotation column to lgd_evaluations table...');
    
    // Add annotation column to lgd_evaluations table
    const { error: lgdError } = await supabase.rpc('exec_sql', {
      sql: 'ALTER TABLE lgd_evaluations ADD COLUMN IF NOT EXISTS annotation TEXT;'
    });
    
    if (lgdError) {
      console.error('Error adding annotation column to lgd_evaluations:', lgdError);
    } else {
      console.log('✓ Added annotation column to lgd_evaluations table');
    }

    console.log('Migration completed!');
  } catch (error) {
    console.error('Migration failed:', error);
  }
}

// Run the migration
addAnnotationColumns();
