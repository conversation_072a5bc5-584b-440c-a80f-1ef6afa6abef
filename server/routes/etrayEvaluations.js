const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const etrayGeminiService = require('../services/etrayGemini');
const supabase = require('../supabaseClient');

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = 'uploads/etray';
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    // Generate unique filename with timestamp
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
  }
});

const upload = multer({ 
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  },
  fileFilter: function (req, file, cb) {
    // Accept PDF, TXT, CSV files
    const allowedTypes = ['.pdf', '.txt', '.csv'];
    const fileExt = path.extname(file.originalname).toLowerCase();
    
    if (allowedTypes.includes(fileExt)) {
      cb(null, true);
    } else {
      cb(new Error('Only PDF, TXT, and CSV files are allowed'));
    }
  }
});

// Background processing function
async function processEtrayEvaluationAsync(evaluationId, input, prompt1Content, prompt2Content) {
  try {
    console.log(`Starting background processing for E-tray evaluation ${evaluationId}`);

    // Run the E-tray prompt chain
    const result = await etrayGeminiService.runEtrayPromptChain(input, prompt1Content, prompt2Content);

    // Update the record with the result
    const { error: updateError } = await supabase
      .from('etray_evaluations')
      .update({
        output: result.finalOutput,
        details: result,
        status: 'completed'
      })
      .eq('id', evaluationId);

    if (updateError) {
      console.error('Error updating E-tray evaluation:', updateError);
      // Update status to error
      await supabase
        .from('etray_evaluations')
        .update({ status: 'error' })
        .eq('id', evaluationId);
    } else {
      console.log(`E-tray evaluation ${evaluationId} completed successfully`);
    }

    // Clean up uploaded files after processing
    if (input.workingResultFile && input.workingResultFile.path) {
      try {
        fs.unlinkSync(input.workingResultFile.path);
        console.log('Cleaned up working result file:', input.workingResultFile.path);
      } catch (cleanupError) {
        console.error('Error cleaning up working result file:', cleanupError);
      }
    }

    if (input.answerKeyFile && input.answerKeyFile.path) {
      try {
        fs.unlinkSync(input.answerKeyFile.path);
        console.log('Cleaned up answer key file:', input.answerKeyFile.path);
      } catch (cleanupError) {
        console.error('Error cleaning up answer key file:', cleanupError);
      }
    }

  } catch (error) {
    console.error(`Error processing E-tray evaluation ${evaluationId}:`, error);
    // Update status to error
    await supabase
      .from('etray_evaluations')
      .update({ status: 'error' })
      .eq('id', evaluationId);

    // Clean up files on error as well
    if (input.workingResultFile && input.workingResultFile.path) {
      try {
        fs.unlinkSync(input.workingResultFile.path);
      } catch (cleanupError) {
        console.error('Error cleaning up working result file on error:', cleanupError);
      }
    }

    if (input.answerKeyFile && input.answerKeyFile.path) {
      try {
        fs.unlinkSync(input.answerKeyFile.path);
      } catch (cleanupError) {
        console.error('Error cleaning up answer key file on error:', cleanupError);
      }
    }
  }
}

// GET all E-tray evaluations
router.get('/', async (req, res) => {
  try {
    const { data, error } = await supabase
      .from('etray_evaluations')
      .select('*')
      .order('timestamp', { ascending: false });

    if (error) {
      console.error('Error fetching E-tray evaluations:', error);
      return res.status(500).json({ error: 'Failed to fetch E-tray evaluations' });
    }

    res.json(data);
  } catch (error) {
    console.error('Error in GET /etray-evaluations:', error);
    res.status(500).json({ error: 'Failed to fetch E-tray evaluations' });
  }
});

// GET specific E-tray evaluation status
router.get('/:id/status', async (req, res) => {
  try {
    const { id } = req.params;

    const { data, error } = await supabase
      .from('etray_evaluations')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching E-tray evaluation status:', error);
      return res.status(500).json({ error: 'Failed to fetch E-tray evaluation status' });
    }

    if (!data) {
      return res.status(404).json({ error: 'E-tray evaluation not found' });
    }

    res.json(data);
  } catch (error) {
    console.error('Error in GET /etray-evaluations/:id/status:', error);
    res.status(500).json({ error: 'Failed to fetch E-tray evaluation status' });
  }
});

// PUT update E-tray evaluation annotation
router.put('/:id/annotation', async (req, res) => {
  try {
    const { id } = req.params;
    const { annotation } = req.body;

    const { data, error } = await supabase
      .from('etray_evaluations')
      .update({ annotation })
      .eq('id', id)
      .select('*')
      .single();

    if (error) {
      console.error('Error updating E-tray evaluation annotation:', error);
      return res.status(500).json({ error: 'Failed to update annotation' });
    }

    res.json(data);
  } catch (error) {
    console.error('Error in PUT /etray-evaluations/:id/annotation:', error);
    res.status(500).json({ error: 'Failed to update annotation' });
  }
});

// POST run new E-tray evaluation with file uploads
router.post('/run', upload.fields([
  { name: 'workingResult', maxCount: 1 },
  { name: 'answerKey', maxCount: 1 }
]), async (req, res) => {
  try {
    const { transcript, competencies } = req.body;

    if (!transcript || !competencies) {
      return res.status(400).json({
        error: 'Transcript and competencies are required'
      });
    }

    // Get uploaded files
    const workingResultFile = req.files && req.files['workingResult'] ? req.files['workingResult'][0] : null;
    const answerKeyFile = req.files && req.files['answerKey'] ? req.files['answerKey'][0] : null;

    // Log file information for debugging
    if (workingResultFile) {
      console.log('Working result file received:', {
        originalname: workingResultFile.originalname,
        filename: workingResultFile.filename,
        mimetype: workingResultFile.mimetype,
        size: workingResultFile.size,
        path: workingResultFile.path
      });
    }

    if (answerKeyFile) {
      console.log('Answer key file received:', {
        originalname: answerKeyFile.originalname,
        filename: answerKeyFile.filename,
        mimetype: answerKeyFile.mimetype,
        size: answerKeyFile.size,
        path: answerKeyFile.path
      });
    }

    // Get E-tray prompts from Supabase (prompts 10 and 11)
    const { data: prompts, error: promptsError } = await supabase
      .from('prompts')
      .select('*')
      .in('id', [10, 11]);

    if (promptsError || !prompts || prompts.length < 2) {
      console.error('Error fetching E-tray prompts:', promptsError);
      return res.status(400).json({ error: 'Required E-tray prompts not found in Supabase' });
    }

    const prompt1 = prompts.find(p => p.id === 10); // E-tray Analysis prompt
    const prompt2 = prompts.find(p => p.id === 11); // E-tray Formatting prompt

    if (!prompt1 || !prompt2) {
      return res.status(400).json({ error: 'Required E-tray prompts not found in Supabase' });
    }

    // Prepare input object
    const input = {
      transcript: transcript.trim(),
      competencies: competencies.trim(),
      workingResultFile: workingResultFile,
      answerKeyFile: answerKeyFile
    };

    // Format input for display
    const formattedInput = `Transcript: ${input.transcript.substring(0, 200)}...\nCompetencies: ${input.competencies.substring(0, 200)}...\nWorking Result: ${workingResultFile ? workingResultFile.originalname : 'Not provided'}\nAnswer Key: ${answerKeyFile ? answerKeyFile.originalname : 'Not provided'}`;

    // Create initial record with "in_progress" status
    const evaluationToInsert = {
      input,
      "formattedInput": formattedInput,
      output: "in_progress",
      status: 'in_progress',
      "prompt1Version": prompt1.version,
      "prompt2Version": prompt2.version,
      "prompt1Content": prompt1.content,
      "prompt2Content": prompt2.content,
      timestamp: new Date().toISOString(),
      annotation: null,
      details: null
    };

    const { data: newEvaluation, error: insertError } = await supabase
      .from('etray_evaluations')
      .insert([evaluationToInsert])
      .select('*')
      .single();

    if (insertError) {
      console.error('Error inserting E-tray evaluation:', insertError);
      return res.status(500).json({ error: 'Failed to save E-tray evaluation' });
    }

    // Start background processing
    processEtrayEvaluationAsync(newEvaluation.id, input, prompt1.content, prompt2.content);

    // Return the evaluation record immediately
    res.json(newEvaluation);
  } catch (error) {
    console.error('Error in POST /etray-evaluations/run:', error);
    res.status(500).json({ error: 'Failed to run E-tray evaluation' });
  }
});

module.exports = router;
