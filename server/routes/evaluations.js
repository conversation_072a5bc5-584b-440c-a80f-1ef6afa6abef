const express = require('express');
const router = express.Router();
const geminiService = require('../services/gemini');
const supabase = require('../supabaseClient'); // Assuming supabaseClient.js is in the server directory

// GET all evaluations
router.get('/', async (req, res) => {
  try {
    const { data, error } = await supabase
      .from('evaluations')
      .select('*')
      .order('timestamp', { ascending: false });

    if (error) {
      console.error('Error fetching evaluations:', error);
      return res.status(500).json({ error: 'Failed to fetch evaluations' });
    }

    res.json(data);
  } catch (error) {
    console.error('Error in GET /evaluations:', error);
    res.status(500).json({ error: 'Failed to fetch evaluations' });
  }
});

// POST run new evaluation
router.post('/run', async (req, res) => {
  try {
    const { input } = req.body;

    if (!input) {
      return res.status(400).json({ error: 'Input is required' });
    }

    // Validate the new input structure
    if (typeof input === 'object' && input.roleName && input.competencyGaps) {
      const { roleName, competencyGaps } = input;

      if (!roleName || !competencyGaps || competencyGaps.length === 0) {
        return res.status(400).json({
          error: 'Role name and competency gaps are required'
        });
      }
    } else {
      // For backward compatibility with old format
      if (typeof input === 'string' && !input.trim()) {
        return res.status(400).json({ error: 'Input text is required' });
      }
    }

    // Get current prompts from Supabase
    const { data: prompts, error: promptsError } = await supabase
      .from('prompts')
      .select('*')
      .in('id', [1, 2]); // Assuming prompt 1 and 2 are the relevant ones

    if (promptsError || !prompts || prompts.length < 2) {
      console.error('Error fetching prompts:', promptsError);
      return res.status(400).json({ error: 'Required prompts not found in Supabase' });
    }

    const prompt1 = prompts.find(p => p.id === 1);
    const prompt2 = prompts.find(p => p.id === 2);

    if (!prompt1 || !prompt2) {
       return res.status(400).json({ error: 'Required prompts not found in Supabase' });
    }


    // Run the prompt chain
    const result = await geminiService.runPromptChain(input, prompt1.content, prompt2.content);

    // Format input for display and extract objectives
    let formattedInput = input;
    let objectives = [];

    if (typeof input === 'object' && input.roleName && input.competencyGaps) {
      // Format competency gaps for clean display
      const competencyGapsList = input.competencyGaps.map(gap =>
        `${gap.competency_name}: ${gap.gap_percentage}%`
      ).join(', ');
      formattedInput = `Role: ${input.roleName}\nCompetency Gaps: ${competencyGapsList}`;

      // Extract objectives from result if available
      if (result.objectives) {
        objectives = result.objectives;
      } else if (result.step1 && result.step1.output) {
        try {
          // Fallback: Parse objectives from step1 output
          const objectivesMatch = result.step1.output.match(/Generated objectives: (\[.*\])/);
          if (objectivesMatch) {
            objectives = JSON.parse(objectivesMatch[1]);
          }
        } catch (error) {
          console.error('Error parsing objectives:', error);
          objectives = [];
        }
      }
    }

    // Insert evaluation result into Supabase
    const evaluationToInsert = {
      input,
      "formattedInput": formattedInput,
      objectives,
      output: result.finalOutput,
      "prompt1Version": prompt1.version,
      "prompt2Version": prompt2.version,
      "prompt1Content": prompt1.content,
      "prompt2Content": prompt2.content,
      timestamp: new Date().toISOString(),
      details: result
    };

    const { data: newEvaluation, error: insertError } = await supabase
      .from('evaluations')
      .insert([evaluationToInsert])
      .select('*') // Select the inserted row
      .single();


    if (insertError) {
      console.error('Error inserting evaluation:', insertError);
      return res.status(500).json({ error: 'Failed to save evaluation' });
    }

    res.json(newEvaluation);
  } catch (error) {
    console.error('Error running evaluation:', error);
    res.status(500).json({ error: 'Failed to run evaluation' });
  }
});

// PUT update evaluation annotation
router.put('/:id/annotation', async (req, res) => {
  try {
    const { id } = req.params;
    const { annotation } = req.body;

    if (!!annotation && !['Good', 'Not Good'].includes(annotation)) {
      return res.status(400).json({ error: 'Annotation must be either "Good" or "Not Good"' });
    }

    const { data, error } = await supabase
      .from('evaluations')
      .update({ annotation })
      .eq('id', id)
      .select('*')
      .single();

    if (error) {
      console.error('Error updating evaluation annotation:', error);
      return res.status(500).json({ error: 'Failed to update annotation' });
    }

    if (!data) {
      return res.status(404).json({ error: 'Evaluation not found' });
    }

    res.json(data);
  } catch (error) {
    console.error('Error in PUT /evaluations/:id/annotation:', error);
    res.status(500).json({ error: 'Failed to update annotation' });
  }
});

module.exports = router;
