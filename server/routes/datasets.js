const express = require('express');
const router = express.Router();
const supabase = require('../supabaseClient');

// GET all datasets with optional label filtering
router.get('/', async (req, res) => {
  try {
    const { label } = req.query;

    let query = supabase
      .from('datasets')
      .select('*')
      .order('createdAt', { ascending: false });

    // Filter by label if provided
    if (label) {
      query = query.eq('label', label);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching datasets:', error);
      return res.status(500).json({ error: 'Failed to fetch datasets' });
    }

    res.json(data);
  } catch (error) {
    console.error('Error in GET /datasets:', error);
    res.status(500).json({ error: 'Failed to fetch datasets' });
  }
});

// GET dataset by ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const { data, error } = await supabase
      .from('datasets')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching dataset:', error);
      return res.status(500).json({ error: 'Failed to fetch dataset' });
    }

    if (!data) {
      return res.status(404).json({ error: 'Dataset not found' });
    }

    res.json(data);
  } catch (error) {
    console.error('Error in GET /datasets/:id:', error);
    res.status(500).json({ error: 'Failed to fetch dataset' });
  }
});

// POST create new dataset
router.post('/', async (req, res) => {
  try {
    const { name, description, data, label } = req.body;

    if (!name || !data) {
      return res.status(400).json({ error: 'Name and data are required' });
    }

    // Validate data structure
    if (!Array.isArray(data.data)) {
      return res.status(400).json({ error: 'Data must contain a "data" array' });
    }

    // Validate each item in the data array

    // answer can be answer or answer_url
    for (const item of data.data) {
      if (!item.question || (!item.answer && !item.answer_url)) {
        return res.status(400).json({ 
          error: 'Each data item must have "question" and "answer" or "answer_url" fields' 
        });
      }
    }

    const datasetToInsert = {
      name,
      description: description || '',
      data,
      label: label || 'ai_interview', // Default to ai_interview for backward compatibility
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    const { data: newDataset, error: insertError } = await supabase
      .from('datasets')
      .insert([datasetToInsert])
      .select('*')
      .single();

    if (insertError) {
      console.error('Error inserting dataset:', insertError);
      return res.status(500).json({ error: 'Failed to save dataset' });
    }

    res.json(newDataset);
  } catch (error) {
    console.error('Error in POST /datasets:', error);
    res.status(500).json({ error: 'Failed to create dataset' });
  }
});

// PUT update dataset
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, data, label } = req.body;

    if (!name || !data) {
      return res.status(400).json({ error: 'Name and data are required' });
    }

    // Validate data structure
    if (!Array.isArray(data.data)) {
      return res.status(400).json({ error: 'Data must contain a "data" array' });
    }

    // Validate each item in the data array
    for (const item of data.data) {
      if (!item.question || !item.answer) {
        return res.status(400).json({ 
          error: 'Each data item must have "question" and "answer" fields' 
        });
      }
    }

    const updateData = {
      name,
      description: description || '',
      data,
      label: label || 'ai_interview', // Default to ai_interview for backward compatibility
      updatedAt: new Date().toISOString()
    };

    const { data: updatedDataset, error: updateError } = await supabase
      .from('datasets')
      .update(updateData)
      .eq('id', id)
      .select('*')
      .single();

    if (updateError) {
      console.error('Error updating dataset:', updateError);
      return res.status(500).json({ error: 'Failed to update dataset' });
    }

    if (!updatedDataset) {
      return res.status(404).json({ error: 'Dataset not found' });
    }

    res.json(updatedDataset);
  } catch (error) {
    console.error('Error in PUT /datasets/:id:', error);
    res.status(500).json({ error: 'Failed to update dataset' });
  }
});

// DELETE dataset
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Check if dataset is being used in any evaluations
    const evaluationTables = [
      'ai_interview_evaluations',
      'ai_interview_v2_evaluations',
      'english_proficiency_evaluations',
      'loi_evaluations'
    ];

    for (const table of evaluationTables) {
      const { data: evaluations, error: checkError } = await supabase
        .from(table)
        .select('id')
        .eq('dataset_id', id)
        .limit(1);

      if (checkError) {
        console.error(`Error checking dataset usage in ${table}:`, checkError);
        return res.status(500).json({ error: 'Failed to check dataset usage' });
      }

      if (evaluations && evaluations.length > 0) {
        return res.status(400).json({
          error: 'Cannot delete dataset that is being used in evaluations'
        });
      }
    }

    const { error: deleteError } = await supabase
      .from('datasets')
      .delete()
      .eq('id', id);

    if (deleteError) {
      console.error('Error deleting dataset:', deleteError);
      return res.status(500).json({ error: 'Failed to delete dataset' });
    }

    res.json({ message: 'Dataset deleted successfully' });
  } catch (error) {
    console.error('Error in DELETE /datasets/:id:', error);
    res.status(500).json({ error: 'Failed to delete dataset' });
  }
});

module.exports = router;
