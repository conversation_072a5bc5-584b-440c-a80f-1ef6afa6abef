const express = require('express');
const router = express.Router();
const supabase = require('../supabaseClient'); // Assuming supabaseClient.js is in the server directory

// GET all prompts
router.get('/', async (req, res) => {
  try {
    const { data, error } = await supabase
      .from('prompts')
      .select('*')
      .order('id', { ascending: true });

    if (error) {
      console.error('Error fetching prompts:', error);
      return res.status(500).json({ error: 'Failed to fetch prompts' });
    }

    res.json(data);
  } catch (error) {
    console.error('Error in GET /prompts:', error);
    res.status(500).json({ error: 'Failed to fetch prompts' });
  }
});

// GET specific prompt by ID
router.get('/:id', async (req, res) => {
  try {
    const { data, error } = await supabase
      .from('prompts')
      .select('*')
      .eq('id', req.params.id)
      .single();

    if (error) {
      console.error('Error fetching prompt by ID:', error);
      if (error.code === 'PGRST116') { // No rows found
        return res.status(404).json({ error: 'Prompt not found' });
      }
      return res.status(500).json({ error: 'Failed to fetch prompt' });
    }

    res.json(data);
  } catch (error) {
    console.error('Error in GET /prompts/:id:', error);
    res.status(500).json({ error: 'Failed to fetch prompt' });
  }
});

// PUT update prompt (creates new version)
router.put('/:id', async (req, res) => {
  try {
    const { content } = req.body;
    const promptId = req.params.id;

    // Fetch the current prompt to get the current version
    const { data: currentPrompt, error: fetchError } = await supabase
      .from('prompts')
      .select('version')
      .eq('id', promptId)
      .single();

    if (fetchError) {
      console.error('Error fetching current prompt version:', fetchError);
      if (fetchError.code === 'PGRST116') { // No rows found
        return res.status(404).json({ error: 'Prompt not found' });
      }
      return res.status(500).json({ error: 'Failed to update prompt' });
    }

    const newVersion = currentPrompt.version + 1;
    const now = new Date().toISOString();

    // Update the prompt
    const { data: updatedPrompt, error: updateError } = await supabase
      .from('prompts')
      .update({
        content: content,
        version: newVersion,
        updatedAt: now
      })
      .eq('id', promptId)
      .select('*') // Select the updated row
      .single();

    if (updateError) {
      console.error('Error updating prompt:', updateError);
      return res.status(500).json({ error: 'Failed to update prompt' });
    }

    res.json(updatedPrompt);
  } catch (error) {
    console.error('Error in PUT /prompts/:id:', error);
    res.status(500).json({ error: 'Failed to update prompt' });
  }
});

// GET specific prompt version
// This route will need to be re-evaluated based on how history is stored in Supabase.
// For now, it will return a 501 Not Implemented error.
router.get('/:id/versions/:version', (req, res) => {
  res.status(501).json({ error: 'Fetching specific prompt versions is not yet implemented with Supabase.' });
});


module.exports = router;
