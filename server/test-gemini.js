const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '..', '.env') });

console.log('Environment check:');
console.log('GEMINI_API_KEY exists:', !!process.env.GEMINI_API_KEY);
console.log('GEMINI_API_KEY length:', process.env.GEMINI_API_KEY ? process.env.GEMINI_API_KEY.length : 0);

async function testGemini() {
  try {
    console.log('Loading Gemini service...');
    const gemini = require('./services/gemini');
    
    console.log('Testing generateResponse...');
    const result = await gemini.generateResponse('Hello, respond with a simple JSON object with a greeting message.');
    
    console.log('Success:', result);
  } catch (error) {
    console.error('Error:', error);
  }
}

testGemini();
