const fs = require('fs').promises;
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '..', '..', '.env') });
const supabase = require('../supabaseClient');

async function runLabelMigration() {
  try {
    console.log('Running dataset label migration...\n');

    // Read and execute the migration SQL
    const migrationPath = path.join(__dirname, '../../supabase/migrations/2025_07_23_11_00_00_add_label_to_datasets.sql');
    const migrationSQL = await fs.readFile(migrationPath, 'utf8');
    
    // Split the SQL into individual statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    console.log(`Executing ${statements.length} SQL statements...\n`);

    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      console.log(`${i + 1}. ${statement.substring(0, 50)}...`);
      
      const { error } = await supabase.rpc('exec_sql', { sql: statement });
      
      if (error) {
        console.error(`Error executing statement ${i + 1}:`, error);
        // Continue with other statements
      } else {
        console.log(`✓ Statement ${i + 1} executed successfully`);
      }
    }

    console.log('\n--- Verifying migration results ---');

    // Check the datasets table structure
    const { data: datasets, error: datasetsError } = await supabase
      .from('datasets')
      .select('id, name, label')
      .order('id');

    if (datasetsError) {
      console.error('Error fetching datasets:', datasetsError);
    } else {
      console.log('\nCurrent datasets with labels:');
      datasets.forEach(dataset => {
        console.log(`- ID: ${dataset.id}, Name: "${dataset.name}", Label: "${dataset.label}"`);
      });
    }

    console.log('\nMigration completed!');
  } catch (error) {
    console.error('Error running migration:', error);
  }
}

runLabelMigration();
