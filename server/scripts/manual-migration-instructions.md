# Manual Migration Instructions

Since the Supabase client doesn't support direct SQL execution, please run the following SQL commands manually in the Supabase dashboard:

## Step 1: Add label column to datasets table

```sql
ALTER TABLE datasets ADD COLUMN label TEXT;
```

## Step 2: Update existing datasets with appropriate labels

```sql
-- Update Sample Interview Dataset
UPDATE datasets 
SET label = 'ai_interview' 
WHERE name = 'Sample Interview Dataset';

-- Update LoI Scoring Dataset  
UPDATE datasets 
SET label = 'loi' 
WHERE name = 'LoI Scoring Dataset';

-- Set default label for any other existing datasets
UPDATE datasets 
SET label = 'ai_interview' 
WHERE label IS NULL;
```

## Step 3: Create index for better performance

```sql
CREATE INDEX idx_datasets_label ON datasets(label);
```

## Step 4: Verify the migration

```sql
SELECT id, name, label FROM datasets ORDER BY id;
```

After running these commands, the dataset filtering should work correctly:
- AI Interview, AI Interview V2, and English Proficiency evaluations will only show datasets with label 'ai_interview'
- LoI evaluations will only show datasets with label 'loi'
