const fs = require('fs').promises;
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '..', '..', '.env') });
const supabase = require('../supabaseClient');

async function addLoIPrompt() {
  try {
    console.log('Adding LoI Scoring Prompt to database...\n');

    // Check if prompt already exists
    const { data: existingPrompt, error: checkError } = await supabase
      .from('prompts')
      .select('id, name, version')
      .eq('id', 16)
      .single();

    if (existingPrompt) {
      console.log(`LoI Scoring Prompt already exists (ID: ${existingPrompt.id}, Version: ${existingPrompt.version})`);
      return;
    }

    if (checkError && checkError.code !== 'PGRST116') {
      console.error('Error checking for existing prompt:', checkError);
      return;
    }

    // Read the prompt content from file
    const promptPath = path.join(__dirname, '../data/loi_prompt.txt');
    const promptContent = await fs.readFile(promptPath, 'utf8');

    // Insert the prompt
    const { data: newPrompt, error: insertError } = await supabase
      .from('prompts')
      .insert([{
        id: 16,
        name: 'LoI Scoring Prompt',
        content: promptContent,
        version: 1,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }])
      .select('*')
      .single();

    if (insertError) {
      console.error('Error inserting LoI prompt:', insertError);
      return;
    }

    console.log('✓ Successfully added LoI Scoring Prompt to database');
    console.log(`  - ID: ${newPrompt.id}`);
    console.log(`  - Name: ${newPrompt.name}`);
    console.log(`  - Version: ${newPrompt.version}`);
    console.log(`  - Content length: ${newPrompt.content.length} characters`);

  } catch (error) {
    console.error('Error adding LoI prompt:', error);
  }
}

// Run the script
addLoIPrompt();
