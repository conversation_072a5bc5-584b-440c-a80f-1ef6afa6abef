const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '..', '..', '.env') });
const supabase = require('../supabaseClient');

async function testLoIPrompt() {
  try {
    console.log('Testing LoI Prompt functionality...\n');

    // Test 1: Check if prompt exists in database
    console.log('1. Checking if LoI prompt exists in database...');
    const { data: prompt, error: promptError } = await supabase
      .from('prompts')
      .select('*')
      .eq('id', 16)
      .single();

    if (promptError) {
      console.error('❌ Error fetching LoI prompt:', promptError);
      return;
    }

    if (!prompt) {
      console.error('❌ LoI prompt not found in database');
      return;
    }

    console.log('✓ LoI prompt found in database');
    console.log(`  - ID: ${prompt.id}`);
    console.log(`  - Name: ${prompt.name}`);
    console.log(`  - Version: ${prompt.version}`);
    console.log(`  - Content length: ${prompt.content.length} characters`);

    // Test 2: Check if prompt content contains expected keywords
    console.log('\n2. Validating prompt content...');
    const expectedKeywords = ['Continuous Learning', 'Driving for result', 'competency analysis', '{{text}}'];
    const missingKeywords = expectedKeywords.filter(keyword => !prompt.content.includes(keyword));

    if (missingKeywords.length > 0) {
      console.error('❌ Missing expected keywords:', missingKeywords);
      return;
    }

    console.log('✓ Prompt content contains all expected keywords');

    // Test 3: Check if all prompts are accessible (including LoI)
    console.log('\n3. Checking all prompts...');
    const { data: allPrompts, error: allPromptsError } = await supabase
      .from('prompts')
      .select('id, name')
      .order('id');

    if (allPromptsError) {
      console.error('❌ Error fetching all prompts:', allPromptsError);
      return;
    }

    console.log('✓ All prompts accessible:');
    allPrompts.forEach(p => {
      const marker = p.id === 16 ? '👉' : '  ';
      console.log(`${marker} ID ${p.id}: ${p.name}`);
    });

    console.log('\n🎉 All tests passed! LoI prompt is ready for use.');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testLoIPrompt();
