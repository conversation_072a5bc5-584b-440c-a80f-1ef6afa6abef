const fs = require('fs').promises;
const path = require('path');
const csv = require('csv-parser');
const { Readable } = require('stream');
require('dotenv').config({ path: path.join(__dirname, '..', '..', '.env') });

// Mock the LoI Gemini service for testing
class MockLoIGeminiService {
  // Parse expected results from CSV format
  parseExpectedResults(expectedResultsStr) {
    try {
      const expectedResults = JSON.parse(expectedResultsStr);
      const continuousLearning = expectedResults.find(comp => comp.competency === "Continuous Learning");
      const drivingForResult = expectedResults.find(comp => comp.competency === "Driving for Result");
      
      return {
        continuousLearningScore: this.getCompetencyScore(continuousLearning),
        drivingForResultScore: this.getCompetencyScore(drivingForResult)
      };
    } catch (error) {
      console.error('Error parsing expected results:', error);
      return { continuousLearningScore: 0, drivingForResultScore: 0 };
    }
  }

  // Calculate the highest level with check=true for a competency
  getCompetencyScore(competencyData) {
    if (!competencyData || !competencyData.levels) return 0;
    
    let highestScore = 0;
    for (const level of competencyData.levels) {
      if (level.check === true && level.order > highestScore) {
        highestScore = level.order;
      }
    }
    return highestScore;
  }

  // Mock Gemini response - for testing, we'll return random scores
  async mockGeminiResponse(inputText) {
    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 100));

    // Generate mock response with random scores for testing
    const continuousLearningScore = Math.floor(Math.random() * 5) + 1;
    const drivingForResultScore = Math.floor(Math.random() * 5) + 1;

    return {
      results: [
        {
          competency: "Continuous Learning",
          levels: Array.from({ length: 5 }, (_, i) => ({
            level: `Level ${i + 1}`,
            order: i + 1,
            check: i + 1 <= continuousLearningScore
          }))
        },
        {
          competency: "Driving for result",
          levels: Array.from({ length: 5 }, (_, i) => ({
            level: `Level ${i + 1}`,
            order: i + 1,
            check: i + 1 <= drivingForResultScore
          }))
        }
      ]
    };
  }

  async runLoIEvaluation(datasetRows, promptContent) {
    try {
      const results = [];
      let continuousLearningCorrect = 0;
      let drivingForResultCorrect = 0;
      const totalRows = datasetRows.length;

      console.log(`Starting mock LoI evaluation for ${totalRows} rows...`);

      for (let i = 0; i < datasetRows.length; i++) {
        const row = datasetRows[i];
        const inputText = row.input_text;
        const expectedResults = this.parseExpectedResults(row.expected_results);

        console.log(`Processing row ${i + 1}/${totalRows}...`);

        try {
          // Mock Gemini response
          const geminiResponse = await this.mockGeminiResponse(inputText);

          const continuousLearning = geminiResponse.results?.find(comp => comp.competency === "Continuous Learning");
          const drivingForResult = geminiResponse.results?.find(comp => comp.competency === "Driving for result");

          const actualScores = {
            continuousLearningScore: this.getCompetencyScore(continuousLearning),
            drivingForResultScore: this.getCompetencyScore(drivingForResult)
          };

          // Calculate differences and check if correct
          const continuousLearningDiff = actualScores.continuousLearningScore - expectedResults.continuousLearningScore;
          const drivingForResultDiff = actualScores.drivingForResultScore - expectedResults.drivingForResultScore;

          if (continuousLearningDiff === 0) continuousLearningCorrect++;
          if (drivingForResultDiff === 0) drivingForResultCorrect++;

          results.push({
            rowIndex: i + 1,
            inputText: inputText.substring(0, 100) + '...', // Truncate for display
            expectedContinuousLearning: expectedResults.continuousLearningScore,
            actualContinuousLearning: actualScores.continuousLearningScore,
            continuousLearningDiff: continuousLearningDiff,
            expectedDrivingForResult: expectedResults.drivingForResultScore,
            actualDrivingForResult: actualScores.drivingForResultScore,
            drivingForResultDiff: drivingForResultDiff,
            geminiResponse: geminiResponse
          });

        } catch (error) {
          console.error(`Error processing row ${i + 1}:`, error);
          results.push({
            rowIndex: i + 1,
            inputText: inputText.substring(0, 100) + '...',
            expectedContinuousLearning: expectedResults.continuousLearningScore,
            actualContinuousLearning: 0,
            continuousLearningDiff: -expectedResults.continuousLearningScore,
            expectedDrivingForResult: expectedResults.drivingForResultScore,
            actualDrivingForResult: 0,
            drivingForResultDiff: -expectedResults.drivingForResultScore,
            error: error.message
          });
        }
      }

      // Calculate accuracy percentages
      const continuousLearningAccuracy = totalRows > 0 ? continuousLearningCorrect / totalRows : 0;
      const drivingForResultAccuracy = totalRows > 0 ? drivingForResultCorrect / totalRows : 0;

      console.log(`Mock LoI evaluation completed. Continuous Learning accuracy: ${(continuousLearningAccuracy * 100).toFixed(1)}%, Driving for Result accuracy: ${(drivingForResultAccuracy * 100).toFixed(1)}%`);

      return {
        results: results,
        summary: {
          totalRows: totalRows,
          continuousLearningCorrect: continuousLearningCorrect,
          continuousLearningAccuracy: continuousLearningAccuracy,
          drivingForResultCorrect: drivingForResultCorrect,
          drivingForResultAccuracy: drivingForResultAccuracy
        }
      };

    } catch (error) {
      console.error('Error in mock LoI evaluation:', error);
      throw error;
    }
  }
}

// Parse CSV data from string
function parseCSVFromString(csvString) {
  return new Promise((resolve, reject) => {
    const results = [];
    const stream = Readable.from([csvString]);
    
    stream
      .pipe(csv())
      .on('data', (data) => results.push(data))
      .on('end', () => resolve(results))
      .on('error', (error) => reject(error));
  });
}

async function testLoIEvaluation() {
  try {
    console.log('Testing LoI evaluation functionality...\n');

    // Read the CSV file
    const csvPath = path.join(__dirname, '../data/dataset_9d39a752-e14e-40fb-8cf5-11c1d3c8338a.csv');
    const csvContent = await fs.readFile(csvPath, 'utf8');

    // Parse CSV data
    const datasetRows = await parseCSVFromString(csvContent);
    console.log(`Loaded ${datasetRows.length} rows from dataset`);

    // Read LoI prompt
    const promptPath = path.join(__dirname, '../data/loi_prompt.txt');
    const promptContent = await fs.readFile(promptPath, 'utf8');
    console.log('Loaded LoI prompt');

    // Test with first 3 rows for quick testing
    const testRows = datasetRows.slice(0, 3);
    console.log(`Testing with first ${testRows.length} rows...\n`);

    // Run mock evaluation
    const mockService = new MockLoIGeminiService();
    const result = await mockService.runLoIEvaluation(testRows, promptContent);

    // Display results
    console.log('\n=== EVALUATION RESULTS ===');
    console.log(`Total rows processed: ${result.summary.totalRows}`);
    console.log(`Continuous Learning accuracy: ${(result.summary.continuousLearningAccuracy * 100).toFixed(1)}%`);
    console.log(`Driving for Result accuracy: ${(result.summary.drivingForResultAccuracy * 100).toFixed(1)}%`);

    console.log('\n=== DETAILED RESULTS ===');
    result.results.forEach((row, index) => {
      console.log(`\nRow ${row.rowIndex}:`);
      console.log(`  Expected CL: ${row.expectedContinuousLearning}, Actual CL: ${row.actualContinuousLearning}, Diff: ${row.continuousLearningDiff}`);
      console.log(`  Expected DR: ${row.expectedDrivingForResult}, Actual DR: ${row.actualDrivingForResult}, Diff: ${row.drivingForResultDiff}`);
    });

    console.log('\n✅ LoI evaluation test completed successfully!');

  } catch (error) {
    console.error('❌ Error testing LoI evaluation:', error);
  }
}

testLoIEvaluation();
