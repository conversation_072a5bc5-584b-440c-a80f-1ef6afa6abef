const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '..', '..', '.env') });
const supabase = require('../supabaseClient');

async function addLabelColumn() {
  try {
    console.log('Adding label column to datasets table...\n');

    // Step 1: Add the label column
    console.log('1. Adding label column...');
    const { error: alterError } = await supabase.rpc('exec_sql', { 
      sql: 'ALTER TABLE datasets ADD COLUMN IF NOT EXISTS label TEXT' 
    });
    
    if (alterError) {
      console.error('Error adding column:', alterError);
    } else {
      console.log('✓ Label column added successfully');
    }

    // Step 2: Update existing datasets with appropriate labels
    console.log('\n2. Updating existing datasets...');
    
    // Update Sample Interview Dataset
    const { error: updateSampleError } = await supabase
      .from('datasets')
      .update({ label: 'ai_interview' })
      .eq('name', 'Sample Interview Dataset');
    
    if (updateSampleError) {
      console.error('Error updating Sample Interview Dataset:', updateSampleError);
    } else {
      console.log('✓ Updated Sample Interview Dataset with ai_interview label');
    }

    // Update LoI Scoring Dataset
    const { error: updateLoiError } = await supabase
      .from('datasets')
      .update({ label: 'loi' })
      .eq('name', 'LoI Scoring Dataset');
    
    if (updateLoiError) {
      console.error('Error updating LoI Scoring Dataset:', updateLoiError);
    } else {
      console.log('✓ Updated LoI Scoring Dataset with loi label');
    }

    // Set default label for any datasets without labels
    const { error: updateNullError } = await supabase
      .from('datasets')
      .update({ label: 'ai_interview' })
      .is('label', null);
    
    if (updateNullError) {
      console.error('Error updating datasets with null labels:', updateNullError);
    } else {
      console.log('✓ Updated datasets with null labels to ai_interview');
    }

    // Step 3: Create index
    console.log('\n3. Creating index...');
    const { error: indexError } = await supabase.rpc('exec_sql', { 
      sql: 'CREATE INDEX IF NOT EXISTS idx_datasets_label ON datasets(label)' 
    });
    
    if (indexError) {
      console.error('Error creating index:', indexError);
    } else {
      console.log('✓ Index created successfully');
    }

    // Step 4: Verify results
    console.log('\n4. Verifying results...');
    const { data: datasets, error: selectError } = await supabase
      .from('datasets')
      .select('id, name, label')
      .order('id');

    if (selectError) {
      console.error('Error fetching datasets:', selectError);
    } else {
      console.log(`\nFound ${datasets.length} datasets:`);
      datasets.forEach(dataset => {
        console.log(`  - ID: ${dataset.id}, Name: "${dataset.name}", Label: "${dataset.label}"`);
      });
    }

    console.log('\nMigration completed successfully!');
  } catch (error) {
    console.error('Error running migration:', error);
  }
}

addLabelColumn();
