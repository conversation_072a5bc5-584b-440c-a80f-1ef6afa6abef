const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '..', '..', '.env') });
const supabase = require('../supabaseClient');

async function testDatasetFiltering() {
  try {
    console.log('Testing dataset filtering by label...\n');

    // Test 1: Get all datasets
    console.log('1. Getting all datasets:');
    const { data: allDatasets, error: allError } = await supabase
      .from('datasets')
      .select('id, name, label')
      .order('id');

    if (allError) {
      console.error('Error:', allError);
    } else {
      console.log(`Found ${allDatasets.length} total datasets:`);
      allDatasets.forEach(dataset => {
        console.log(`  - ${dataset.name} (label: ${dataset.label})`);
      });
    }

    // Test 2: Get AI Interview datasets
    console.log('\n2. Getting AI Interview datasets (label: ai_interview):');
    const { data: aiDatasets, error: aiError } = await supabase
      .from('datasets')
      .select('id, name, label')
      .eq('label', 'ai_interview')
      .order('id');

    if (aiError) {
      console.error('Error:', aiError);
    } else {
      console.log(`Found ${aiDatasets.length} AI Interview datasets:`);
      aiDatasets.forEach(dataset => {
        console.log(`  - ${dataset.name} (label: ${dataset.label})`);
      });
    }

    // Test 3: Get LoI datasets
    console.log('\n3. Getting LoI datasets (label: loi):');
    const { data: loiDatasets, error: loiError } = await supabase
      .from('datasets')
      .select('id, name, label')
      .eq('label', 'loi')
      .order('id');

    if (loiError) {
      console.error('Error:', loiError);
    } else {
      console.log(`Found ${loiDatasets.length} LoI datasets:`);
      loiDatasets.forEach(dataset => {
        console.log(`  - ${dataset.name} (label: ${dataset.label})`);
      });
    }

    console.log('\nDataset filtering test completed!');
  } catch (error) {
    console.error('Error testing dataset filtering:', error);
  }
}

testDatasetFiltering();
