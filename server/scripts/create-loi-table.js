const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '..', '..', '.env') });
const supabase = require('../supabaseClient');

async function createLoITable() {
  try {
    // Since we can't run DDL directly through Supabase client,
    // let's just test if the table exists by trying to query it
    const { data, error } = await supabase
      .from('loi_evaluations')
      .select('id')
      .limit(1);

    if (error && error.code === '42P01') {
      console.log('LoI evaluations table does not exist. Please run the migration manually in Supabase dashboard:');
      console.log(`
-- Create the loi_evaluations table for LoI scoring evaluations
CREATE TABLE loi_evaluations (
    id SERIAL PRIMARY KEY,
    dataset_id INT REFERENCES datasets(id),
    dataset_name TEXT,
    output JSONB,
    status VARCHAR(50) DEFAULT 'in_progress',
    prompt_version INT,
    prompt_content TEXT,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    details JSONB,
    annotation TEXT,
    -- Accuracy metrics for the two competencies
    continuous_learning_accuracy DECIMAL(5,4), -- e.g., 0.8000 for 80%
    driving_for_result_accuracy DECIMAL(5,4),   -- e.g., 0.7000 for 70%
    total_rows_processed INT DEFAULT 0
);

-- Add indexes for better query performance
CREATE INDEX idx_loi_evaluations_timestamp ON loi_evaluations(timestamp);
CREATE INDEX idx_loi_evaluations_status ON loi_evaluations(status);
CREATE INDEX idx_loi_evaluations_dataset_id ON loi_evaluations(dataset_id);
      `);
    } else if (error) {
      console.error('Error checking LoI table:', error);
    } else {
      console.log('LoI evaluations table already exists');
    }
  } catch (error) {
    console.error('Error creating LoI table:', error);
  }
}

createLoITable();
