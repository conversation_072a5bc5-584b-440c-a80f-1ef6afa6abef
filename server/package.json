{"name": "prompt-evals-server", "version": "1.0.0", "description": "Backend server for prompt evaluation app", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "engines": {"node": ">=22.16.0"}, "dependencies": {"@google/genai": "^1.0.0", "@supabase/supabase-js": "^2.49.8", "cors": "^2.8.5", "csv-parser": "^3.2.0", "dotenv": "^16.5.0", "express": "^4.21.2", "express-basic-auth": "^1.2.1", "form-data": "^4.0.0", "multer": "^1.4.5-lts.1"}, "devDependencies": {"nodemon": "^3.1.10"}}