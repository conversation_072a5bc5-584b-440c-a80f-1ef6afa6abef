{"name": "prompt-evals-client", "private": true, "version": "0.0.0", "type": "module", "engines": {"node": ">=22.16.0"}, "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"axios": "^1.9.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-markdown": "^10.1.0", "remark-gfm": "^4.0.1"}, "devDependencies": {"@types/react": "^18.3.22", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react": "^4.5.0", "eslint": "^8.57.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.20", "vite": "^5.4.19"}}