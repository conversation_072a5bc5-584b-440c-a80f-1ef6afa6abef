import React, { useState, useRef, useEffect } from 'react';

const AnnotationSelector = ({ 
  evaluationId, 
  currentAnnotation, 
  onAnnotationUpdate, 
  disabled = false 
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [openTowardsUp, setOpenTowardsUp] = useState(false);
  const selectorRef = useRef(null);

  useEffect(() => {
    if (!isOpen || !selectorRef.current) return;

    const handleScroll = () => {
      if (!selectorRef.current) return;
      const rect = selectorRef.current.getBoundingClientRect();
      const spaceBelow = window.innerHeight - rect.bottom;
      const spaceAbove = rect.top;
      
      // Assuming dropdown height is approx 120px (adjust as needed)
      const dropdownHeight = 120;

      if (spaceBelow < dropdownHeight && spaceAbove > dropdownHeight) {
        setOpenTowardsUp(true);
      } else {
        setOpenTowardsUp(false);
      }
    };

    handleScroll(); // Initial check
    window.addEventListener('scroll', handleScroll, true); // Use capture phase

    return () => {
      window.removeEventListener('scroll', handleScroll, true);
    };
  }, [isOpen]);

  const handleAnnotationSelect = async (annotation) => {
    setIsUpdating(true);
    try {
      await onAnnotationUpdate(evaluationId, annotation);
      setIsOpen(false);
    } catch (error) {
      console.error('Failed to update annotation:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const getAnnotationColor = (annotation) => {
    switch (annotation) {
      case 'Good':
        return '#28a745';
      case 'Not Good':
        return '#dc3545';
      default:
        return '#6c757d';
    }
  };

  const getAnnotationIcon = (annotation) => {
    switch (annotation) {
      case 'Good':
        return '👍';
      case 'Not Good':
        return '👎';
      default:
        return '📝';
    }
  };

  return (
    <div
      style={{ position: 'relative', display: 'inline-block' }}
      ref={selectorRef}
    >
      {/* Current annotation display or annotate button */}
      {currentAnnotation ? (
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <span
            style={{
              padding: '4px 8px',
              borderRadius: '12px',
              fontSize: '12px',
              fontWeight: '500',
              backgroundColor: getAnnotationColor(currentAnnotation) + '20',
              color: getAnnotationColor(currentAnnotation),
              border: `1px solid ${getAnnotationColor(currentAnnotation)}40`,
              display: 'flex',
              alignItems: 'center',
              gap: '4px'
            }}
          >
            {getAnnotationIcon(currentAnnotation)} {currentAnnotation}
          </span>
          {!disabled && (
            <button
              onClick={() => setIsOpen(!isOpen)}
              disabled={isUpdating}
              style={{
                padding: '2px 6px',
                border: '1px solid #6c757d',
                borderRadius: '4px',
                backgroundColor: 'white',
                color: '#6c757d',
                cursor: isUpdating ? 'not-allowed' : 'pointer',
                fontSize: '10px',
                opacity: isUpdating ? 0.6 : 1
              }}
            >
              {isUpdating ? '...' : 'Edit'}
            </button>
          )}
        </div>
      ) : (
        <button
          onClick={() => setIsOpen(!isOpen)}
          disabled={disabled || isUpdating}
          style={{
            padding: '4px 8px',
            border: '1px solid #007bff',
            borderRadius: '4px',
            backgroundColor: 'white',
            color: '#007bff',
            cursor: (disabled || isUpdating) ? 'not-allowed' : 'pointer',
            fontSize: '12px',
            opacity: (disabled || isUpdating) ? 0.6 : 1
          }}
        >
          {isUpdating ? 'Updating...' : '📝 Annotate'}
        </button>
      )}

      {/* Dropdown menu */}
      {isOpen && !disabled && (
        <div
          style={{
            position: 'absolute',
            [openTowardsUp ? 'bottom' : 'top']: openTowardsUp ? '100%' : '100%',
            left: 0,
            zIndex: 1000,
            backgroundColor: 'white',
            border: '1px solid #dee2e6',
            borderRadius: '4px',
            boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
            minWidth: '120px',
            [openTowardsUp ? 'marginBottom' : 'marginTop']: '4px'
          }}
        >
          <button
            onClick={() => handleAnnotationSelect('Good')}
            disabled={isUpdating}
            style={{
              width: '100%',
              padding: '8px 12px',
              border: 'none',
              backgroundColor: 'white',
              color: '#28a745',
              cursor: isUpdating ? 'not-allowed' : 'pointer',
              fontSize: '12px',
              textAlign: 'left',
              display: 'flex',
              alignItems: 'center',
              gap: '6px',
              opacity: isUpdating ? 0.6 : 1
            }}
            onMouseEnter={(e) => e.target.style.backgroundColor = '#f8f9fa'}
            onMouseLeave={(e) => e.target.style.backgroundColor = 'white'}
          >
            👍 Good
          </button>
          <button
            onClick={() => handleAnnotationSelect('Not Good')}
            disabled={isUpdating}
            style={{
              width: '100%',
              padding: '8px 12px',
              border: 'none',
              backgroundColor: 'white',
              color: '#dc3545',
              cursor: isUpdating ? 'not-allowed' : 'pointer',
              fontSize: '12px',
              textAlign: 'left',
              display: 'flex',
              alignItems: 'center',
              gap: '6px',
              opacity: isUpdating ? 0.6 : 1
            }}
            onMouseEnter={(e) => e.target.style.backgroundColor = '#f8f9fa'}
            onMouseLeave={(e) => e.target.style.backgroundColor = 'white'}
          >
            👎 Not Good
          </button>
          {currentAnnotation && (
            <>
              <hr style={{ margin: '4px 0', border: 'none', borderTop: '1px solid #dee2e6' }} />
              <button
                onClick={() => handleAnnotationSelect(null)}
                disabled={isUpdating}
                style={{
                  width: '100%',
                  padding: '8px 12px',
                  border: 'none',
                  backgroundColor: 'white',
                  color: '#6c757d',
                  cursor: isUpdating ? 'not-allowed' : 'pointer',
                  fontSize: '12px',
                  textAlign: 'left',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '6px',
                  opacity: isUpdating ? 0.6 : 1
                }}
                onMouseEnter={(e) => e.target.style.backgroundColor = '#f8f9fa'}
                onMouseLeave={(e) => e.target.style.backgroundColor = 'white'}
              >
                🗑️ Remove
              </button>
            </>
          )}
        </div>
      )}

      {/* Backdrop to close dropdown */}
      {isOpen && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            zIndex: 999
          }}
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
};

export default AnnotationSelector;
