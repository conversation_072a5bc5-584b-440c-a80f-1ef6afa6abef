import React, { useState } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

const PromptEditor = ({ prompt, onUpdate }) => {
  const [content, setContent] = useState(prompt.content);
  const [isEditing, setIsEditing] = useState(false);
  const [saving, setSaving] = useState(false);

  const handleSave = async () => {
    if (content.trim() === prompt.content.trim()) {
      setIsEditing(false);
      return;
    }

    try {
      setSaving(true);
      await onUpdate(prompt.id, content.trim());
      setIsEditing(false);
    } catch (error) {
      alert('Failed to save prompt');
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    setContent(prompt.content);
    setIsEditing(false);
  };

  return (
    <div style={{
      border: '1px solid #ddd',
      borderRadius: '8px',
      padding: '20px',
      backgroundColor: 'white',
      boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
      height: 'fit-content'
    }}>
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '12px'
      }}>
        <h3 style={{ margin: 0 }}>{prompt.name}</h3>
        <div style={{ fontSize: '12px', color: '#666' }}>
          Version: {prompt.version}
        </div>
      </div>

      {isEditing ? (
        <div>
          <textarea
            value={content}
            onChange={(e) => setContent(e.target.value)}
            style={{
              width: '100%',
              minHeight: '150px',
              maxHeight: '768px',
              padding: '12px',
              border: '1px solid #ccc',
              borderRadius: '4px',
              fontSize: '14px',
              fontFamily: 'monospace',
              resize: 'vertical',
              lineHeight: '1.5',
              overflowY: 'auto'
            }}
            placeholder="Enter your prompt here..."
          />
          <div style={{
            display: 'flex',
            gap: '8px',
            marginTop: '8px',
            justifyContent: 'flex-end'
          }}>
            <button
              onClick={handleCancel}
              disabled={saving}
              style={{
                padding: '6px 12px',
                border: '1px solid #ccc',
                borderRadius: '4px',
                backgroundColor: 'white',
                cursor: 'pointer'
              }}
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              disabled={saving}
              style={{
                padding: '6px 12px',
                border: 'none',
                borderRadius: '4px',
                backgroundColor: '#007bff',
                color: 'white',
                cursor: 'pointer'
              }}
            >
              {saving ? 'Saving...' : 'Save'}
            </button>
          </div>
        </div>
      ) : (
        <div>
          <div style={{
            padding: '12px',
            backgroundColor: '#f8f9fa',
            border: '1px solid #e9ecef',
            borderRadius: '4px',
            minHeight: '150px',
            maxHeight: '768px',
            fontSize: '14px',
            fontFamily: 'monospace',
            whiteSpace: 'pre-wrap',
            overflowX: 'auto',
            overflowY: 'auto',
            overflowWrap: 'break-word',
            lineHeight: '1.5'
          }}>
            <ReactMarkdown remarkPlugins={[remarkGfm]}>{prompt.content}</ReactMarkdown>
          </div>
          <div style={{
            display: 'flex',
            justifyContent: 'flex-end',
            marginTop: '8px'
          }}>
            <button
              onClick={() => setIsEditing(true)}
              style={{
                padding: '6px 12px',
                border: '1px solid #007bff',
                borderRadius: '4px',
                backgroundColor: 'white',
                color: '#007bff',
                cursor: 'pointer'
              }}
            >
              Edit
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default PromptEditor;
