import React, { useState } from 'react';
import PromptModal from './PromptModal';
import AnnotationSelector from './AnnotationSelector';
import { promptsApi, etrayEvaluationsApi } from '../services/api';

// Modal component for displaying input content (transcript)
const InputContentModal = ({ isOpen, onClose, title, content }) => {
  if (!isOpen) return null;

  return (
    <div
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 10000
      }}
      onClick={onClose}
    >
      <div
        style={{
          backgroundColor: 'white',
          borderRadius: '8px',
          padding: '20px',
          maxWidth: '80%',
          maxHeight: '80vh',
          overflow: 'auto',
          position: 'relative'
        }}
        onClick={(e) => e.stopPropagation()}
      >
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '20px',
          borderBottom: '1px solid #eee',
          paddingBottom: '10px'
        }}>
          <h3 style={{ margin: 0 }}>{title}</h3>
          <button
            onClick={onClose}
            style={{
              background: 'none',
              border: 'none',
              fontSize: '24px',
              cursor: 'pointer',
              color: '#666'
            }}
          >
            ×
          </button>
        </div>
        <div style={{
          fontFamily: 'monospace',
          fontSize: '14px',
          lineHeight: '1.5',
          whiteSpace: 'pre-wrap',
          wordBreak: 'break-word',
          maxHeight: '768px',
          overflowY: 'auto'
        }}>
          {content}
        </div>
      </div>
    </div>
  );
};

const EtrayResultsTable = ({ evaluations, onAnnotationUpdate }) => {
  const [expandedRow, setExpandedRow] = useState(null);
  const [inputModalState, setInputModalState] = useState({
    isOpen: false,
    title: '',
    content: ''
  });
  const [promptModalState, setPromptModalState] = useState({
    isOpen: false,
    promptId: null,
    promptName: '',
    promptContent: ''
  });
  const [copyStates, setCopyStates] = useState({}); // Track copy states for each evaluation

  const toggleRow = (id) => {
    setExpandedRow(expandedRow === id ? null : id);
  };

  const openInputModal = (title, content) => {
    setInputModalState({
      isOpen: true,
      title,
      content
    });
  };

  const closeInputModal = () => {
    setInputModalState({
      isOpen: false,
      title: '',
      content: ''
    });
  };

  const openPromptModal = async (promptId, promptName) => {
    try {
      const response = await promptsApi.getById(promptId);
      setPromptModalState({
        isOpen: true,
        promptId,
        promptName,
        promptContent: response.data.content
      });
    } catch (error) {
      console.error('Error fetching prompt:', error);
      alert('Failed to load prompt');
    }
  };

  const closePromptModal = () => {
    setPromptModalState({
      isOpen: false,
      promptId: null,
      promptName: '',
      promptContent: ''
    });
  };

  const handlePromptUpdate = async (promptId, newContent) => {
    try {
      await promptsApi.update(promptId, newContent);
      setPromptModalState(prev => ({
        ...prev,
        promptContent: newContent
      }));
      alert('Prompt updated successfully');
    } catch (error) {
      console.error('Error updating prompt:', error);
      alert('Failed to update prompt');
    }
  };

  const handleAnnotationUpdate = async (evaluationId, annotation) => {
    try {
      const response = await etrayEvaluationsApi.updateAnnotation(evaluationId, annotation);
      if (onAnnotationUpdate) {
        onAnnotationUpdate(response.data);
      }
    } catch (error) {
      console.error('Failed to update annotation:', error);
      throw error;
    }
  };

  // Copy to clipboard function
  const handleCopyAnalysisOutput = async (evaluationId, output) => {
    try {
      const formattedOutput = typeof output === 'string' ? output : JSON.stringify(output, null, 2);
      await navigator.clipboard.writeText(formattedOutput);

      // Set success state
      setCopyStates(prev => ({ ...prev, [evaluationId]: 'success' }));

      // Reset state after 2 seconds
      setTimeout(() => {
        setCopyStates(prev => ({ ...prev, [evaluationId]: null }));
      }, 2000);
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);

      // Set error state
      setCopyStates(prev => ({ ...prev, [evaluationId]: 'error' }));

      // Reset state after 2 seconds
      setTimeout(() => {
        setCopyStates(prev => ({ ...prev, [evaluationId]: null }));
      }, 2000);
    }
  };

  // Helper function to truncate text for bullet points
  const truncateText = (text, maxLength = 100) => {
    if (!text) return 'No content available';
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
  };

  // Helper function to parse input data
  const parseInputData = (evaluation) => {
    try {
      let transcriptContent = 'No transcript available';
      let competenciesContent = 'No competencies available';
      let workingResultFile = 'No working result file';
      let answerKeyFile = 'No answer key file';

      // Try to get from input object first (full data)
      if (evaluation.input && typeof evaluation.input === 'object') {
        transcriptContent = evaluation.input.transcript || 'No transcript available';
        competenciesContent = evaluation.input.competencies || 'No competencies available';

        // Handle file information
        if (evaluation.input.workingResultFile) {
          workingResultFile = evaluation.input.workingResultFile.originalname || 'Working result file uploaded';
        }
        if (evaluation.input.answerKeyFile) {
          answerKeyFile = evaluation.input.answerKeyFile.originalname || 'Answer key file uploaded';
        }
      }

      return {
        transcript: transcriptContent,
        competencies: competenciesContent,
        workingResultFile,
        answerKeyFile
      };
    } catch (error) {
      console.error('Error parsing input data:', error);
      return {
        transcript: 'Error parsing transcript',
        competencies: 'Error parsing competencies',
        workingResultFile: 'Error parsing working result file',
        answerKeyFile: 'Error parsing answer key file'
      };
    }
  };

  const renderOutput = (evaluation) => {
    if (evaluation.status !== 'completed' || !evaluation.output) {
      return <span style={{ color: '#6c757d', fontStyle: 'italic' }}>No output available</span>;
    }

    try {
      const output = typeof evaluation.output === 'string' ? JSON.parse(evaluation.output) : evaluation.output;
      
      if (output.result && Array.isArray(output.result)) {
        return (
          <div>
            <h4 style={{ margin: '10px 0', color: '#495057' }}>Analysis Results:</h4>
            {output.result.map((participant, index) => (
              <div key={index} style={{
                border: '1px solid #dee2e6',
                borderRadius: '4px',
                padding: '10px',
                marginBottom: '10px',
                backgroundColor: '#f8f9fa'
              }}>
                <h5 style={{ margin: '0 0 8px 0', color: '#495057' }}>
                  {participant.participant_name || `Participant ${participant.user_id || index + 1}`}
                  {participant.overall_score && (
                    <span style={{ marginLeft: '10px', color: '#28a745', fontWeight: 'bold' }}>
                      Overall Score: {participant.overall_score}
                    </span>
                  )}
                </h5>
                
                {participant.detail_scores && participant.detail_scores.length > 0 && (
                  <div style={{ marginTop: '8px' }}>
                    <strong>Competency Scores:</strong>
                    <ul style={{ margin: '5px 0', paddingLeft: '20px' }}>
                      {participant.detail_scores.map((competency, compIndex) => (
                        <li key={compIndex} style={{ marginBottom: '4px' }}>
                          <strong>{competency.competency_name}:</strong> {competency.average_score}
                          {competency.aspects && competency.aspects.length > 0 && (
                            <ul style={{ marginTop: '4px', paddingLeft: '15px' }}>
                              {competency.aspects.map((aspect, aspectIndex) => (
                                <li key={aspectIndex} style={{ fontSize: '12px', color: '#6c757d' }}>
                                  {aspect.aspect_name}: Level {aspect.scale_level}
                                </li>
                              ))}
                            </ul>
                          )}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            ))}
          </div>
        );
      } else {
        return (
          <div style={{ fontFamily: 'monospace', fontSize: '12px', color: '#495057' }}>
            {JSON.stringify(output, null, 2)}
          </div>
        );
      }
    } catch (error) {
      return (
        <div style={{ fontFamily: 'monospace', fontSize: '12px', color: '#495057' }}>
          {evaluation.output}
        </div>
      );
    }
  };

  if (!evaluations || evaluations.length === 0) {
    return (
      <div style={{
        textAlign: 'center',
        padding: '40px',
        color: '#6c757d',
        backgroundColor: '#f8f9fa',
        borderRadius: '8px',
        border: '1px solid #dee2e6'
      }}>
        <h3>No E-tray Analysis Results</h3>
        <p>Run an E-tray analysis to see results here.</p>
      </div>
    );
  }

  return (
    <div>
      <h2 style={{ marginBottom: '20px', color: '#495057' }}>
        📧 E-tray Analysis Results ({evaluations.length})
      </h2>

      <div style={{
        border: '1px solid #dee2e6',
        borderRadius: '8px',
        overflow: 'hidden',
        backgroundColor: 'white'
      }}>
        <table style={{ width: '100%', borderCollapse: 'collapse' }}>
          <thead style={{ backgroundColor: '#f8f9fa' }}>
            <tr>
              <th style={{ padding: '12px', textAlign: 'left', borderBottom: '2px solid #dee2e6' }}>
                Timestamp
              </th>
              <th style={{ padding: '12px', textAlign: 'left', borderBottom: '2px solid #dee2e6' }}>
                Input Summary
              </th>
              <th style={{ padding: '12px', textAlign: 'center', borderBottom: '2px solid #dee2e6' }}>
                Prompts
              </th>
              <th style={{ padding: '12px', textAlign: 'center', borderBottom: '2px solid #dee2e6' }}>
                Annotation
              </th>
              <th style={{ padding: '12px', textAlign: 'left', borderBottom: '2px solid #dee2e6' }}>
                Actions
              </th>
            </tr>
          </thead>
          <tbody>
            {evaluations.map((evaluation) => (
              <React.Fragment key={evaluation.id}>
                <tr style={{
                  borderBottom: '1px solid #eee',
                  backgroundColor: expandedRow === evaluation.id ? '#f8f9fa' : 'white'
                }}>
                  <td style={{ padding: '12px', verticalAlign: 'top' }}>
                    {new Date(evaluation.timestamp).toLocaleString()}
                  </td>
                  <td style={{ padding: '12px', verticalAlign: 'top' }}>
                    {(() => {
                      const inputData = parseInputData(evaluation);
                      return (
                        <div style={{ fontSize: '12px', color: '#495057' }}>
                          <div style={{ marginBottom: '4px' }}>
                            • <strong>Transcript:</strong> {truncateText(inputData.transcript, 60)}
                          </div>
                          <div style={{ marginBottom: '4px' }}>
                            • <strong>Competencies:</strong> {truncateText(inputData.competencies, 60)}
                          </div>
                          <div style={{ marginBottom: '4px' }}>
                            • <strong>Working Result:</strong> {inputData.workingResultFile}
                          </div>
                          <div style={{ marginBottom: '4px' }}>
                            • <strong>Answer Key:</strong> {inputData.answerKeyFile}
                          </div>
                        </div>
                      );
                    })()}
                  </td>
                  <td style={{ padding: '12px', textAlign: 'center', verticalAlign: 'top' }}>
                    <div style={{ display: 'flex', flexDirection: 'column', gap: '4px', alignItems: 'center' }}>
                      {evaluation.prompt1Version && (
                        <button
                          onClick={() => openPromptModal(10, 'E-tray Analysis Prompt')}
                          style={{
                            padding: '2px 6px',
                            border: '1px solid #28a745',
                            borderRadius: '3px',
                            backgroundColor: 'white',
                            color: '#28a745',
                            cursor: 'pointer',
                            fontSize: '10px'
                          }}
                        >
                          Analysis v{evaluation.prompt1Version}
                        </button>
                      )}
                      {evaluation.prompt2Version && (
                        <button
                          onClick={() => openPromptModal(11, 'E-tray Formatting Prompt')}
                          style={{
                            padding: '2px 6px',
                            border: '1px solid #17a2b8',
                            borderRadius: '3px',
                            backgroundColor: 'white',
                            color: '#17a2b8',
                            cursor: 'pointer',
                            fontSize: '10px'
                          }}
                        >
                          Format v{evaluation.prompt2Version}
                        </button>
                      )}
                    </div>
                  </td>
                  <td style={{ padding: '12px', textAlign: 'center', verticalAlign: 'top' }}>
                    <AnnotationSelector
                      evaluationId={evaluation.id}
                      currentAnnotation={evaluation.annotation}
                      onAnnotationUpdate={handleAnnotationUpdate}
                    />
                  </td>
                  <td style={{ padding: '12px', verticalAlign: 'top' }}>
                    {(() => {
                      const isInProgress = evaluation.status === "in_progress";
                      const isError = evaluation.status === "error";

                      if (isInProgress) {
                        return (
                          <span style={{
                            padding: '4px 8px',
                            backgroundColor: '#fff3cd',
                            color: '#856404',
                            borderRadius: '4px',
                            fontSize: '12px',
                            fontWeight: 'bold'
                          }}>
                            ⏳ Processing...
                          </span>
                        );
                      }

                      if (isError) {
                        return (
                          <span style={{
                            padding: '4px 8px',
                            backgroundColor: '#f8d7da',
                            color: '#721c24',
                            borderRadius: '4px',
                            fontSize: '12px',
                            fontWeight: 'bold'
                          }}>
                            ❌ Error
                          </span>
                        );
                      }

                      return (
                        <button
                          onClick={() => toggleRow(evaluation.id)}
                          style={{
                            padding: '6px 12px',
                            border: '1px solid #007bff',
                            borderRadius: '4px',
                            backgroundColor: expandedRow === evaluation.id ? '#007bff' : 'white',
                            color: expandedRow === evaluation.id ? 'white' : '#007bff',
                            cursor: 'pointer',
                            fontSize: '12px'
                          }}
                        >
                          {expandedRow === evaluation.id ? 'Hide' : 'Show'} Details
                        </button>
                      );
                    })()}
                  </td>
                </tr>

                {expandedRow === evaluation.id && evaluation.status === 'completed' && (
                  <tr>
                    <td colSpan="5" style={{ padding: '20px', backgroundColor: '#f8f9fa' }}>
                      <div style={{ display: 'grid', gap: '20px' }}>
                        <div>
                          <h4 style={{ marginBottom: '10px', color: '#495057' }}>📝 Input Data</h4>
                          <div style={{
                            backgroundColor: 'white',
                            padding: '15px',
                            borderRadius: '4px',
                            border: '1px solid #dee2e6'
                          }}>
                            {(() => {
                              const inputData = parseInputData(evaluation);
                              return (
                                <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                                  <div>
                                    <strong>Transcript:</strong>
                                    <button
                                      onClick={() => openInputModal('E-tray Transcript', inputData.transcript)}
                                      style={{
                                        marginLeft: '8px',
                                        padding: '4px 8px',
                                        border: '1px solid #007bff',
                                        borderRadius: '4px',
                                        backgroundColor: 'white',
                                        color: '#007bff',
                                        cursor: 'pointer',
                                        fontSize: '12px'
                                      }}
                                    >
                                      View Full
                                    </button>
                                    <div style={{
                                      marginTop: '8px',
                                      padding: '8px',
                                      backgroundColor: '#f8f9fa',
                                      borderRadius: '4px',
                                      fontSize: '12px',
                                      fontFamily: 'monospace',
                                      maxHeight: '100px',
                                      overflow: 'hidden'
                                    }}>
                                      {truncateText(inputData.transcript, 200)}
                                    </div>
                                  </div>
                                  <div>
                                    <strong>Competencies:</strong>
                                    <button
                                      onClick={() => openInputModal('Competency Guidelines', inputData.competencies)}
                                      style={{
                                        marginLeft: '8px',
                                        padding: '4px 8px',
                                        border: '1px solid #007bff',
                                        borderRadius: '4px',
                                        backgroundColor: 'white',
                                        color: '#007bff',
                                        cursor: 'pointer',
                                        fontSize: '12px'
                                      }}
                                    >
                                      View Full
                                    </button>
                                    <div style={{
                                      marginTop: '8px',
                                      padding: '8px',
                                      backgroundColor: '#f8f9fa',
                                      borderRadius: '4px',
                                      fontSize: '12px',
                                      maxHeight: '100px',
                                      overflow: 'hidden'
                                    }}>
                                      {truncateText(inputData.competencies, 200)}
                                    </div>
                                  </div>
                                  <div>
                                    <strong>Working Result File:</strong> {inputData.workingResultFile}
                                  </div>
                                  <div>
                                    <strong>Answer Key File:</strong> {inputData.answerKeyFile}
                                  </div>
                                </div>
                              );
                            })()}
                          </div>
                        </div>

                        {(evaluation.prompt1Content || evaluation.prompt2Content) && (
                          <div>
                            <h4 style={{ marginBottom: '10px', color: '#495057' }}>🔧 Prompts Used</h4>
                            <div style={{
                              backgroundColor: 'white',
                              padding: '15px',
                              borderRadius: '4px',
                              border: '1px solid #dee2e6'
                            }}>
                              <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                                {evaluation.prompt1Content && (
                                  <div>
                                    <strong>Analysis Prompt (v{evaluation.prompt1Version}):</strong>
                                    <button
                                      onClick={() => openInputModal('E-tray Analysis Prompt', evaluation.prompt1Content)}
                                      style={{
                                        marginLeft: '8px',
                                        padding: '4px 8px',
                                        border: '1px solid #28a745',
                                        borderRadius: '4px',
                                        backgroundColor: 'white',
                                        color: '#28a745',
                                        cursor: 'pointer',
                                        fontSize: '12px'
                                      }}
                                    >
                                      View Full
                                    </button>
                                    <div style={{
                                      marginTop: '8px',
                                      padding: '8px',
                                      backgroundColor: '#f8f9fa',
                                      borderRadius: '4px',
                                      fontSize: '12px',
                                      maxHeight: '100px',
                                      overflow: 'hidden'
                                    }}>
                                      {truncateText(evaluation.prompt1Content, 200)}
                                    </div>
                                  </div>
                                )}
                                {evaluation.prompt2Content && (
                                  <div>
                                    <strong>Formatting Prompt (v{evaluation.prompt2Version}):</strong>
                                    <button
                                      onClick={() => openInputModal('E-tray Formatting Prompt', evaluation.prompt2Content)}
                                      style={{
                                        marginLeft: '8px',
                                        padding: '4px 8px',
                                        border: '1px solid #17a2b8',
                                        borderRadius: '4px',
                                        backgroundColor: 'white',
                                        color: '#17a2b8',
                                        cursor: 'pointer',
                                        fontSize: '12px'
                                      }}
                                    >
                                      View Full
                                    </button>
                                    <div style={{
                                      marginTop: '8px',
                                      padding: '8px',
                                      backgroundColor: '#f8f9fa',
                                      borderRadius: '4px',
                                      fontSize: '12px',
                                      maxHeight: '100px',
                                      overflow: 'hidden'
                                    }}>
                                      {truncateText(evaluation.prompt2Content, 200)}
                                    </div>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        )}

                        <div>
                          <div style={{ display: 'flex', alignItems: 'center', gap: '10px', marginBottom: '10px' }}>
                            <h4 style={{ margin: 0, color: '#495057' }}>📧 E-tray Analysis Output</h4>
                            <button
                              onClick={() => handleCopyAnalysisOutput(evaluation.id, evaluation.output)}
                              style={{
                                padding: '4px 8px',
                                border: '1px solid #28a745',
                                borderRadius: '4px',
                                backgroundColor: copyStates[evaluation.id] === 'success' ? '#28a745' : 'white',
                                color: copyStates[evaluation.id] === 'success' ? 'white' : '#28a745',
                                cursor: 'pointer',
                                fontSize: '12px'
                              }}
                            >
                              {copyStates[evaluation.id] === 'success' ? '✓ Copied!' :
                               copyStates[evaluation.id] === 'error' ? '✗ Failed' : '📋 Copy'}
                            </button>
                          </div>
                          <div style={{
                            backgroundColor: 'white',
                            padding: '15px',
                            borderRadius: '4px',
                            border: '1px solid #dee2e6',
                            fontFamily: 'monospace',
                            fontSize: '12px',
                            lineHeight: '1.4',
                            maxHeight: '400px',
                            overflow: 'auto',
                            whiteSpace: 'pre-wrap'
                          }}>
                            {typeof evaluation.output === 'string' ? evaluation.output : JSON.stringify(evaluation.output, null, 2)}
                          </div>
                        </div>
                      </div>
                    </td>
                  </tr>
                )}
              </React.Fragment>
            ))}
          </tbody>
        </table>
      </div>

      {/* Modals */}
      <InputContentModal
        isOpen={inputModalState.isOpen}
        onClose={closeInputModal}
        title={inputModalState.title}
        content={inputModalState.content}
      />

      <PromptModal
        isOpen={promptModalState.isOpen}
        onClose={closePromptModal}
        promptName={promptModalState.promptName}
        promptData={{ content: promptModalState.promptContent }}
        onSave={(newContent) => handlePromptUpdate(promptModalState.promptId, newContent)}
      />
    </div>
  );
};

export default EtrayResultsTable;
