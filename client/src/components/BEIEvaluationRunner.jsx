import { useState, useEffect, useRef } from 'react';
import { dataApi, beiEvaluationsApi, promptsApi } from '../services/api';
import PromptModal from './PromptModal';

// Modal component for displaying full content
const ContentModal = ({ isOpen, onClose, title, content }) => {
  if (!isOpen) return null;

  return (
    <div
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 10000
      }}
      onClick={onClose}
    >
      <div
        style={{
          backgroundColor: 'white',
          borderRadius: '8px',
          padding: '20px',
          maxWidth: '80%',
          maxHeight: '80vh',
          overflow: 'auto',
          position: 'relative'
        }}
        onClick={(e) => e.stopPropagation()}
      >
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '20px',
          borderBottom: '1px solid #eee',
          paddingBottom: '10px'
        }}>
          <h3 style={{ margin: 0 }}>{title}</h3>
          <button
            onClick={onClose}
            style={{
              background: 'none',
              border: 'none',
              fontSize: '24px',
              cursor: 'pointer',
              color: '#666'
            }}
          >
            ×
          </button>
        </div>
        <div style={{
          fontFamily: 'monospace',
          fontSize: '14px',
          lineHeight: '1.5',
          whiteSpace: 'pre-wrap',
          wordBreak: 'break-word',
          maxHeight: '768px',
          overflowY: 'auto'
        }}>
          {content}
        </div>
      </div>
    </div>
  );
};

const BEIEvaluationRunner = ({ onRun, onPollingUpdate }) => {
  const [transcript, setTranscript] = useState('');
  const [competencies, setCompetencies] = useState('');
  const [running, setRunning] = useState(false);
  const [lastResult, setLastResult] = useState(null);
  const [pollingEvaluations, setPollingEvaluations] = useState(new Set());
  const [modalState, setModalState] = useState({
    isOpen: false,
    title: '',
    content: ''
  });
  const [promptModalState, setPromptModalState] = useState({
    isOpen: false,
    promptId: null,
    promptName: '',
    promptData: ''
  });

  const pollingIntervals = useRef(new Map());

  // Cleanup polling intervals on unmount
  useEffect(() => {
    return () => {
      pollingIntervals.current.forEach(intervalId => clearInterval(intervalId));
      pollingIntervals.current.clear();
    };
  }, []);

  // Start polling for a specific evaluation
  const startPolling = (evaluationId) => {
    if (pollingIntervals.current.has(evaluationId)) {
      return; // Already polling
    }

    setPollingEvaluations(prev => new Set([...prev, evaluationId]));

    const intervalId = setInterval(async () => {
      try {
        const response = await beiEvaluationsApi.getStatus(evaluationId);
        const { status } = response.data;

        if (status === 'completed' || status === 'error') {
          setLastResult(response.data);

          // Stop polling
          clearInterval(intervalId);
          pollingIntervals.current.delete(evaluationId);
          setPollingEvaluations(prev => {
            const newSet = new Set(prev);
            newSet.delete(evaluationId);
            return newSet;
          });

          // Notify parent component to refresh data
          if (onPollingUpdate) {
            onPollingUpdate(evaluationId, status);
          }
        }
      } catch (error) {
        console.error('Error polling evaluation status:', error);
        // Continue polling on error, don't stop
      }
    }, 30000); // Poll every 30 seconds

    pollingIntervals.current.set(evaluationId, intervalId);
  };

  const handleRun = async () => {
    if (!transcript.trim()) {
      alert('Please enter the BEI transcript');
      return;
    }
    if (!competencies.trim()) {
      alert('Please enter the competency guidelines');
      return;
    }

    try {
      setRunning(true);
      const result = await onRun({
        transcript: transcript.trim(),
        competencies: competencies.trim()
      });
      setLastResult(result);

      // Start polling if the result has "in_progress" status
      if (result && result.output === "in_progress") {
        startPolling(result.id);
      }
    } catch (error) {
      alert('Failed to run BEI evaluation');
    } finally {
      setRunning(false);
    }
  };

  const openModal = (title, content) => {
    setModalState({
      isOpen: true,
      title,
      content
    });
  };

  const closeModal = () => {
    setModalState({
      isOpen: false,
      title: '',
      content: ''
    });
  };

  const openPromptModal = async (promptId, promptName) => {
    try {
      const response = await promptsApi.getById(promptId);
      setPromptModalState({
        isOpen: true,
        promptId,
        promptName,
        promptData: response.data
      });
    } catch (error) {
      console.error('Error fetching prompt:', error);
      alert('Failed to load prompt');
    }
  };

  const closePromptModal = () => {
    setPromptModalState({
      isOpen: false,
      promptId: null,
      promptName: '',
      promptData: ''
    });
  };

  const handlePromptUpdate = async (promptId, newContent) => {
    try {
      await promptsApi.update(promptId, newContent);
      setPromptModalState(prev => ({
        ...prev,
        promptData: newContent
      }));
      alert('Prompt updated successfully');
    } catch (error) {
      console.error('Error updating prompt:', error);
      alert('Failed to update prompt');
    }
  };

  const loadSampleData = async () => {
    try {
      // Load transcript from data/bei_transcript.txt (from backend server)
      const transcriptResponse = await dataApi.getBEITranscript();
      if (transcriptResponse.status !== 200) {
        throw new Error(`Failed to fetch transcript: ${transcriptResponse.status}`);
      }
      const sampleTranscript = await transcriptResponse.data;

      try {
        setTranscript(JSON.stringify(sampleTranscript, null, 2));
      } catch (error) {
        setTranscript(sampleTranscript.trim());
      }

      // Load competencies from bei_competencies.txt
      try {
        const competenciesResponse = await fetch('/api/data/bei_competencies.txt');
        if (competenciesResponse.ok) {
          const sampleCompetencies = await competenciesResponse.text();
          setCompetencies(sampleCompetencies.trim());
        }
      } catch (error) {
        console.error('Error loading competencies:', error);
        // Set default competencies if file loading fails
        setCompetencies(`*   **Competencies & Definitions:**
    *   **Accountability:** The ability to take responsibility for actions, decisions, and outcomes, both individually and in a team. This includes owning successes and failures, setting high standards, and ensuring clear commitments to achieve expected results.
    *   **Continuous Learning:** The ability to actively seek opportunities to learn new things and apply them iteratively to discover best practices in one's work.
    *   **Problem Solving:** The ability to systematically identify, analyze, and resolve problems using logical, objective, and open-minded thinking. This involves evaluating assumptions, considering various perspectives, and developing effective solutions with foresight.
    *   **Driving for Results:** The ability to consistently achieve results by setting challenging yet realistic performance goals and optimally managing systems, processes, and resources to meet targets.
    *   **Fostering Collaboration and Partnerships:** The ability to build and maintain strategic relationships inside and outside the organization to achieve shared goals. This includes identifying collaboration opportunities and sharing resources to enhance synergy.
    *   **Strategic Thinking:** The ability to analyze information, identify trends, and design long-term strategies aligned with the organization's vision. This involves formulating alternatives based on logical data and assumptions and translating vision into actionable plans.

*   **Scoring Rubric (1-5 Scale):**
    *   **1 - Basic Awareness:** Demonstrates a theoretical understanding of the competency but provides no concrete examples of application.
    *   **2 - Basic Application:** Can describe past situations where they applied the competency in simple, straightforward tasks with guidance.
    *   **3 - Intermediate:** Independently applies the competency in moderately complex situations. Can analyze the outcome and articulate lessons learned.
    *   **4 - Advanced:** Proactively and consistently applies the competency in complex and ambiguous situations. Can guide others and demonstrates a deep understanding of the nuances. The impact of their actions is significant.
    *   **5 - Mastery:** Is seen as an expert. Shapes strategy and influences others across the organization based on this competency. Innovates processes and sets new standards for excellence in this area.`);
      }
    } catch (error) {
      console.error('Error loading sample data:', error);
      alert('Failed to load sample data from files: ' + error.message);
    }
  };

  return (
    <div style={{
      border: '1px solid #ddd',
      borderRadius: '8px',
      padding: '20px',
      backgroundColor: '#f8f9fa'
    }}>
      <h3 style={{ marginBottom: '20px', color: '#495057' }}>
        🎯 BEI Analysis Evaluation Runner
      </h3>

      <div style={{ marginBottom: '20px' }}>
        <label style={{
          display: 'block',
          marginBottom: '8px',
          fontWeight: 'bold',
          color: '#495057'
        }}>
          BEI Interview Transcript (JSON format):
        </label>
        <div style={{ display: 'flex', gap: '10px', alignItems: 'flex-start' }}>
          <textarea
            value={transcript}
            onChange={(e) => setTranscript(e.target.value)}
            placeholder="Enter the BEI interview transcript in JSON format..."
            style={{
              flex: 1,
              minHeight: '200px',
              padding: '12px',
              border: '1px solid #ced4da',
              borderRadius: '4px',
              fontSize: '14px',
              fontFamily: 'monospace',
              resize: 'vertical'
            }}
          />
          {transcript && (
            <button
              onClick={() => openModal('BEI Transcript', transcript)}
              style={{
                padding: '8px 12px',
                border: '1px solid #007bff',
                borderRadius: '4px',
                backgroundColor: 'white',
                color: '#007bff',
                cursor: 'pointer',
                fontSize: '12px',
                whiteSpace: 'nowrap'
              }}
            >
              View Full
            </button>
          )}
        </div>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <label style={{
          display: 'block',
          marginBottom: '8px',
          fontWeight: 'bold',
          color: '#495057'
        }}>
          Competency Guidelines:
        </label>
        <div style={{ display: 'flex', gap: '10px', alignItems: 'flex-start' }}>
          <textarea
            value={competencies}
            onChange={(e) => setCompetencies(e.target.value)}
            placeholder="Enter the competency guidelines and scoring rubric..."
            style={{
              flex: 1,
              minHeight: '150px',
              padding: '12px',
              border: '1px solid #ced4da',
              borderRadius: '4px',
              fontSize: '14px',
              resize: 'vertical'
            }}
          />
          {competencies && (
            <button
              onClick={() => openModal('Competency Guidelines', competencies)}
              style={{
                padding: '8px 12px',
                border: '1px solid #007bff',
                borderRadius: '4px',
                backgroundColor: 'white',
                color: '#007bff',
                cursor: 'pointer',
                fontSize: '12px',
                whiteSpace: 'nowrap'
              }}
            >
              View Full
            </button>
          )}
        </div>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <label style={{
          display: 'block',
          marginBottom: '8px',
          fontWeight: 'bold',
          color: '#495057'
        }}>
          Analysis Prompts:
        </label>
        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
          <button
            onClick={() => openPromptModal(8, 'BEI Analysis Prompt')}
            style={{
              padding: '8px 16px',
              border: '1px solid #28a745',
              borderRadius: '4px',
              backgroundColor: 'white',
              color: '#28a745',
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            📝 Edit Analysis Prompt
          </button>
          <button
            onClick={() => openPromptModal(9, 'BEI Formatting Prompt')}
            style={{
              padding: '8px 16px',
              border: '1px solid #17a2b8',
              borderRadius: '4px',
              backgroundColor: 'white',
              color: '#17a2b8',
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            🔧 Edit Formatting Prompt
          </button>
        </div>
      </div>

      <div style={{ display: 'flex', gap: '10px', marginBottom: '20px' }}>
        <button
          onClick={handleRun}
          disabled={running || !transcript.trim() || !competencies.trim()}
          style={{
            padding: '10px 20px',
            border: 'none',
            borderRadius: '4px',
            backgroundColor: running ? '#6c757d' : '#28a745',
            color: 'white',
            cursor: running ? 'not-allowed' : 'pointer',
            fontSize: '16px',
            fontWeight: 'bold'
          }}
        >
          {running ? 'Analyzing...' : 'Run BEI Analysis'}
        </button>

        <button
          onClick={loadSampleData}
          disabled={running}
          style={{
            padding: '10px 20px',
            border: '1px solid #007bff',
            borderRadius: '4px',
            backgroundColor: 'white',
            color: '#007bff',
            cursor: 'pointer',
            fontSize: '16px'
          }}
        >
          Load Sample Data
        </button>
      </div>

      {/* Processing notification */}
      {running && (
        <div style={{
          marginBottom: '20px',
          padding: '15px',
          backgroundColor: '#fff3cd',
          border: '1px solid #ffeaa7',
          borderRadius: '4px',
          color: '#856404'
        }}>
          <h4 style={{ margin: '0 0 10px 0', color: '#856404' }}>
            ⏳ Analysis Started
          </h4>
          <p style={{ margin: 0 }}>
            Your BEI analysis request has been submitted and is being processed in the background.
            You can continue using the application - we'll update the results automatically when complete.
          </p>
        </div>
      )}

      {/* Polling notification */}
      {pollingEvaluations.size > 0 && (
        <div style={{
          marginBottom: '20px',
          padding: '15px',
          backgroundColor: '#d1ecf1',
          border: '1px solid #bee5eb',
          borderRadius: '4px',
          color: '#0c5460'
        }}>
          <h4 style={{ margin: '0 0 10px 0', color: '#0c5460' }}>
            🔄 Processing in Background ({pollingEvaluations.size})
          </h4>
          <p style={{ margin: 0 }}>
            {pollingEvaluations.size === 1 ? 'An analysis is' : `${pollingEvaluations.size} analyses are`} currently being processed.
            Results will appear automatically when complete (checking every 30 seconds).
          </p>
        </div>
      )}

      {lastResult && lastResult.status === 'completed' && (
        <div style={{
          marginTop: '20px',
          padding: '15px',
          backgroundColor: '#d4edda',
          border: '1px solid #c3e6cb',
          borderRadius: '4px'
        }}>
          <h4 style={{ margin: '0 0 10px 0', color: '#155724' }}>
            ✅ Analysis Complete
          </h4>
          <p style={{ margin: 0, color: '#155724' }}>
            BEI analysis completed successfully. Check the results table below for detailed output.
          </p>
        </div>
      )}

      {/* Content Modal */}
      <ContentModal
        isOpen={modalState.isOpen}
        onClose={closeModal}
        title={modalState.title}
        content={modalState.content}
      />

      {/* Prompt Modal */}
      <PromptModal
        isOpen={promptModalState.isOpen}
        onClose={closePromptModal}
        promptName={promptModalState.promptName}
        promptData={promptModalState.promptData}
        onSave={(newContent) => handlePromptUpdate(promptModalState.promptId, newContent)}
      />
    </div>
  );
};

export default BEIEvaluationRunner;
