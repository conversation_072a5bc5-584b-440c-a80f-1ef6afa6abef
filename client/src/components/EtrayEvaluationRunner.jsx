import { useState, useEffect, useRef } from 'react';
import { dataApi, etrayEvaluationsApi, promptsApi } from '../services/api';
import PromptModal from './PromptModal';

// Modal component for displaying full content
const ContentModal = ({ isOpen, onClose, title, content }) => {
  if (!isOpen) return null;

  return (
    <div
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 10000
      }}
      onClick={onClose}
    >
      <div
        style={{
          backgroundColor: 'white',
          borderRadius: '8px',
          padding: '20px',
          maxWidth: '80%',
          maxHeight: '80vh',
          overflow: 'auto',
          position: 'relative'
        }}
        onClick={(e) => e.stopPropagation()}
      >
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '20px',
          borderBottom: '1px solid #eee',
          paddingBottom: '10px'
        }}>
          <h3 style={{ margin: 0 }}>{title}</h3>
          <button
            onClick={onClose}
            style={{
              background: 'none',
              border: 'none',
              fontSize: '24px',
              cursor: 'pointer',
              color: '#666'
            }}
          >
            ×
          </button>
        </div>
        <div style={{
          fontFamily: 'monospace',
          fontSize: '14px',
          lineHeight: '1.5',
          whiteSpace: 'pre-wrap',
          wordBreak: 'break-word',
          maxHeight: '768px',
          overflowY: 'auto'
        }}>
          {content}
        </div>
      </div>
    </div>
  );
};

const EtrayEvaluationRunner = ({ onRun, onPollingUpdate }) => {
  const [transcript, setTranscript] = useState('');
  const [competencies, setCompetencies] = useState('');
  const [workingResultFile, setWorkingResultFile] = useState(null);
  const [answerKeyFile, setAnswerKeyFile] = useState(null);
  const [running, setRunning] = useState(false);
  const [lastResult, setLastResult] = useState(null);
  const [pollingEvaluations, setPollingEvaluations] = useState(new Set());
  const [modalState, setModalState] = useState({
    isOpen: false,
    title: '',
    content: ''
  });
  const [promptModalState, setPromptModalState] = useState({
    isOpen: false,
    promptId: null,
    promptName: '',
    promptData: ''
  });

  const pollingIntervals = useRef(new Map());
  const workingResultFileRef = useRef(null);
  const answerKeyFileRef = useRef(null);

  // Cleanup polling intervals on unmount
  useEffect(() => {
    return () => {
      pollingIntervals.current.forEach(intervalId => clearInterval(intervalId));
      pollingIntervals.current.clear();
    };
  }, []);

  // Start polling for a specific evaluation
  const startPolling = (evaluationId) => {
    if (pollingIntervals.current.has(evaluationId)) {
      return; // Already polling
    }

    setPollingEvaluations(prev => new Set([...prev, evaluationId]));

    const intervalId = setInterval(async () => {
      try {
        const response = await etrayEvaluationsApi.getStatus(evaluationId);
        const { status } = response.data;

        if (status === 'completed' || status === 'error') {
          setLastResult(response.data);

          // Stop polling
          clearInterval(intervalId);
          pollingIntervals.current.delete(evaluationId);
          setPollingEvaluations(prev => {
            const newSet = new Set(prev);
            newSet.delete(evaluationId);
            return newSet;
          });

          // Notify parent component to refresh data
          if (onPollingUpdate) {
            onPollingUpdate(evaluationId, status);
          }
        }
      } catch (error) {
        console.error('Error polling evaluation status:', error);
        // Continue polling on error, don't stop
      }
    }, 30000); // Poll every 30 seconds

    pollingIntervals.current.set(evaluationId, intervalId);
  };

  const handleRun = async () => {
    if (!transcript.trim()) {
      alert('Please enter the E-tray transcript');
      return;
    }
    if (!competencies.trim()) {
      alert('Please enter the competency guidelines');
      return;
    }

    try {
      setRunning(true);
      
      // Create FormData for file upload
      const formData = new FormData();
      formData.append('transcript', transcript.trim());
      formData.append('competencies', competencies.trim());
      
      if (workingResultFile) {
        formData.append('workingResult', workingResultFile);
      }
      
      if (answerKeyFile) {
        formData.append('answerKey', answerKeyFile);
      }

      const result = await onRun(formData);
      setLastResult(result);

      // Start polling if the result has "in_progress" status
      if (result && result.output === "in_progress") {
        startPolling(result.id);
      }
    } catch (error) {
      alert('Failed to run E-tray evaluation');
    } finally {
      setRunning(false);
    }
  };

  const openModal = (title, content) => {
    setModalState({
      isOpen: true,
      title,
      content
    });
  };

  const closeModal = () => {
    setModalState({
      isOpen: false,
      title: '',
      content: ''
    });
  };

  const openPromptModal = async (promptId, promptName) => {
    try {
      const response = await promptsApi.getById(promptId);
      setPromptModalState({
        isOpen: true,
        promptId,
        promptName,
        promptData: response.data
      });
    } catch (error) {
      console.error('Error fetching prompt:', error);
      alert('Failed to load prompt');
    }
  };

  const closePromptModal = () => {
    setPromptModalState({
      isOpen: false,
      promptId: null,
      promptName: '',
      promptData: ''
    });
  };

  const handlePromptUpdate = async (promptId, newContent) => {
    try {
      await promptsApi.update(promptId, newContent);
      setPromptModalState(prev => ({
        ...prev,
        promptData: newContent
      }));
      alert('Prompt updated successfully');
    } catch (error) {
      console.error('Error updating prompt:', error);
      alert('Failed to update prompt');
    }
  };

  const loadSampleData = async () => {
    try {
      // Load transcript from data/etray_transcript.txt (from backend server)
      const transcriptResponse = await dataApi.getEtrayTranscript();
      if (transcriptResponse.status !== 200) {
        throw new Error(`Failed to fetch transcript: ${transcriptResponse.status}`);
      }
      const sampleTranscript = await transcriptResponse.data;

      try {
        setTranscript(JSON.stringify(sampleTranscript, null, 2));
      } catch (error) {
        setTranscript(sampleTranscript.trim());
      }

      // Load competencies from etray_competencies.txt
      try {
        const competenciesResponse = await fetch('/api/data/etray_competencies.txt');
        if (competenciesResponse.ok) {
          const sampleCompetencies = await competenciesResponse.text();
          setCompetencies(sampleCompetencies.trim());
        }
      } catch (error) {
        console.error('Error loading competencies:', error);
        // Set default competencies if file loading fails
        setCompetencies(`### Accountability:
Level 1
KB 1: Mampu menyelesaikan tugas pribadi sesuai instruksi
Where we measure : response, confirmatory_answer
Level 2
KB 4: Dapat menyusun rencana kerja pribadi untuk mencapai target
Where to measure : response, confirmatory_answer
Level 3
KB 1: Mampu mengatur prioritas kerja agar target tercapai secara konsisten
Where to measure : priority
Unique notes : aspect is achieved when %priority_match is more than or similar with 60%. %priority_match = the number of 'priority' match with 'answer_key__email_priority' / total number of email

### Problem Solving:
Level 1
KB 1: Mengidentifikasi masalah dasar yang terjadi dalam pekerjaan harian 
Where to measure : response, confirmatory_answeer

KB 2: Menyampaikan permasalahan kepada pihak yang tepat
Where we measure : response, cc_bcc
Unique notes : aspect is achieved when %collaboration_accuracy is more than or similar with 60%. %collaboration_accuracy = total number of cc_bcc or response accurately mentioning answer_key__cc_bcc / total number of email with answer_key_cc_bcc not blank

KB 3: Mencoba solusi yang pernah berhasil sebelumnya
Where we measure : response
Level 2
KB 3: Menggunakan data untuk mendukung pemecahan masalah
Where we measure : response, confirmatory_answer`);
      }
    } catch (error) {
      console.error('Error loading sample data:', error);
      alert('Failed to load sample data from files: ' + error.message);
    }
  };

  const handleFileChange = (event, fileType) => {
    const file = event.target.files[0];
    if (file) {
      if (fileType === 'workingResult') {
        setWorkingResultFile(file);
      } else if (fileType === 'answerKey') {
        setAnswerKeyFile(file);
      }
    }
  };

  const clearFile = (fileType) => {
    if (fileType === 'workingResult') {
      setWorkingResultFile(null);
      if (workingResultFileRef.current) {
        workingResultFileRef.current.value = '';
      }
    } else if (fileType === 'answerKey') {
      setAnswerKeyFile(null);
      if (answerKeyFileRef.current) {
        answerKeyFileRef.current.value = '';
      }
    }
  };

  return (
    <div style={{
      border: '1px solid #ddd',
      borderRadius: '8px',
      padding: '20px',
      backgroundColor: '#f8f9fa'
    }}>
      <h3 style={{ marginBottom: '20px', color: '#495057' }}>
        📧 E-tray Analysis Evaluation Runner
      </h3>

      <div style={{ marginBottom: '20px' }}>
        <label style={{
          display: 'block',
          marginBottom: '8px',
          fontWeight: 'bold',
          color: '#495057'
        }}>
          E-tray Transcript:
        </label>
        <div style={{ display: 'flex', gap: '10px', alignItems: 'flex-start' }}>
          <textarea
            value={transcript}
            onChange={(e) => setTranscript(e.target.value)}
            placeholder="Enter the E-tray simulation transcript..."
            style={{
              flex: 1,
              minHeight: '150px',
              padding: '12px',
              border: '1px solid #ced4da',
              borderRadius: '4px',
              fontSize: '14px',
              fontFamily: 'monospace',
              resize: 'vertical'
            }}
          />
          {transcript && (
            <button
              onClick={() => openModal('E-tray Transcript', transcript)}
              style={{
                padding: '8px 12px',
                border: '1px solid #007bff',
                borderRadius: '4px',
                backgroundColor: 'white',
                color: '#007bff',
                cursor: 'pointer',
                fontSize: '12px',
                whiteSpace: 'nowrap'
              }}
            >
              View Full
            </button>
          )}
        </div>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <label style={{
          display: 'block',
          marginBottom: '8px',
          fontWeight: 'bold',
          color: '#495057'
        }}>
          Competency Guidelines:
        </label>
        <div style={{ display: 'flex', gap: '10px', alignItems: 'flex-start' }}>
          <textarea
            value={competencies}
            onChange={(e) => setCompetencies(e.target.value)}
            placeholder="Enter the competency guidelines and assessment criteria..."
            style={{
              flex: 1,
              minHeight: '150px',
              padding: '12px',
              border: '1px solid #ced4da',
              borderRadius: '4px',
              fontSize: '14px',
              resize: 'vertical'
            }}
          />
          {competencies && (
            <button
              onClick={() => openModal('Competency Guidelines', competencies)}
              style={{
                padding: '8px 12px',
                border: '1px solid #007bff',
                borderRadius: '4px',
                backgroundColor: 'white',
                color: '#007bff',
                cursor: 'pointer',
                fontSize: '12px',
                whiteSpace: 'nowrap'
              }}
            >
              View Full
            </button>
          )}
        </div>
      </div>

      {/* File Upload Section */}
      <div style={{ marginBottom: '20px' }}>
        <label style={{
          display: 'block',
          marginBottom: '8px',
          fontWeight: 'bold',
          color: '#495057'
        }}>
          File Uploads:
        </label>

        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px' }}>
          {/* Working Result File */}
          <div style={{
            border: '1px solid #ced4da',
            borderRadius: '4px',
            padding: '15px',
            backgroundColor: 'white'
          }}>
            <h4 style={{ margin: '0 0 10px 0', color: '#495057', fontSize: '14px' }}>
              📄 Working Result Document
            </h4>
            <input
              type="file"
              ref={workingResultFileRef}
              accept=".pdf,.txt,.csv"
              onChange={(e) => handleFileChange(e, 'workingResult')}
              style={{
                width: '100%',
                padding: '8px',
                border: '1px solid #ced4da',
                borderRadius: '4px',
                fontSize: '14px'
              }}
            />
            {workingResultFile && (
              <div style={{ marginTop: '8px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <span style={{ fontSize: '12px', color: '#28a745' }}>
                  ✓ {workingResultFile.name}
                </span>
                <button
                  onClick={() => clearFile('workingResult')}
                  style={{
                    padding: '4px 8px',
                    border: '1px solid #dc3545',
                    borderRadius: '4px',
                    backgroundColor: 'white',
                    color: '#dc3545',
                    cursor: 'pointer',
                    fontSize: '12px'
                  }}
                >
                  Remove
                </button>
              </div>
            )}
            <p style={{ margin: '8px 0 0 0', fontSize: '12px', color: '#6c757d' }}>
              Upload participant's working document (PDF, TXT, CSV)
            </p>
          </div>

          {/* Answer Key File */}
          <div style={{
            border: '1px solid #ced4da',
            borderRadius: '4px',
            padding: '15px',
            backgroundColor: 'white'
          }}>
            <h4 style={{ margin: '0 0 10px 0', color: '#495057', fontSize: '14px' }}>
              🔑 Answer Key Document
            </h4>
            <input
              type="file"
              ref={answerKeyFileRef}
              accept=".pdf,.txt,.csv"
              onChange={(e) => handleFileChange(e, 'answerKey')}
              style={{
                width: '100%',
                padding: '8px',
                border: '1px solid #ced4da',
                borderRadius: '4px',
                fontSize: '14px'
              }}
            />
            {answerKeyFile && (
              <div style={{ marginTop: '8px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <span style={{ fontSize: '12px', color: '#28a745' }}>
                  ✓ {answerKeyFile.name}
                </span>
                <button
                  onClick={() => clearFile('answerKey')}
                  style={{
                    padding: '4px 8px',
                    border: '1px solid #dc3545',
                    borderRadius: '4px',
                    backgroundColor: 'white',
                    color: '#dc3545',
                    cursor: 'pointer',
                    fontSize: '12px'
                  }}
                >
                  Remove
                </button>
              </div>
            )}
            <p style={{ margin: '8px 0 0 0', fontSize: '12px', color: '#6c757d' }}>
              Upload answer key document (PDF, TXT, CSV)
            </p>
          </div>
        </div>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <label style={{
          display: 'block',
          marginBottom: '8px',
          fontWeight: 'bold',
          color: '#495057'
        }}>
          Analysis Prompts:
        </label>
        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
          <button
            onClick={() => openPromptModal(10, 'E-tray Analysis Prompt')}
            style={{
              padding: '8px 16px',
              border: '1px solid #28a745',
              borderRadius: '4px',
              backgroundColor: 'white',
              color: '#28a745',
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            📝 Edit Analysis Prompt
          </button>
          <button
            onClick={() => openPromptModal(11, 'E-tray Formatting Prompt')}
            style={{
              padding: '8px 16px',
              border: '1px solid #17a2b8',
              borderRadius: '4px',
              backgroundColor: 'white',
              color: '#17a2b8',
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            🔧 Edit Formatting Prompt
          </button>
        </div>
      </div>

      <div style={{ display: 'flex', gap: '10px', marginBottom: '20px' }}>
        <button
          onClick={handleRun}
          disabled={running || !transcript.trim() || !competencies.trim()}
          style={{
            padding: '10px 20px',
            border: 'none',
            borderRadius: '4px',
            backgroundColor: running ? '#6c757d' : '#28a745',
            color: 'white',
            cursor: running ? 'not-allowed' : 'pointer',
            fontSize: '16px',
            fontWeight: 'bold'
          }}
        >
          {running ? 'Analyzing...' : 'Run E-tray Analysis'}
        </button>

        <button
          onClick={loadSampleData}
          disabled={running}
          style={{
            padding: '10px 20px',
            border: '1px solid #007bff',
            borderRadius: '4px',
            backgroundColor: 'white',
            color: '#007bff',
            cursor: 'pointer',
            fontSize: '16px'
          }}
        >
          Load Sample Data
        </button>
      </div>

      {/* Processing notification */}
      {running && (
        <div style={{
          marginBottom: '20px',
          padding: '15px',
          backgroundColor: '#fff3cd',
          border: '1px solid #ffeaa7',
          borderRadius: '4px',
          color: '#856404'
        }}>
          <h4 style={{ margin: '0 0 10px 0', color: '#856404' }}>
            ⏳ Analysis Started
          </h4>
          <p style={{ margin: 0 }}>
            Your E-tray analysis request has been submitted and is being processed in the background.
            You can continue using the application - we'll update the results automatically when complete.
          </p>
        </div>
      )}

      {/* Polling notification */}
      {pollingEvaluations.size > 0 && (
        <div style={{
          marginBottom: '20px',
          padding: '15px',
          backgroundColor: '#d1ecf1',
          border: '1px solid #bee5eb',
          borderRadius: '4px',
          color: '#0c5460'
        }}>
          <h4 style={{ margin: '0 0 10px 0', color: '#0c5460' }}>
            🔄 Processing in Background ({pollingEvaluations.size})
          </h4>
          <p style={{ margin: 0 }}>
            {pollingEvaluations.size === 1 ? 'An analysis is' : `${pollingEvaluations.size} analyses are`} currently being processed.
            Results will appear automatically when complete (checking every 30 seconds).
          </p>
        </div>
      )}

      {lastResult && lastResult.status === 'completed' && (
        <div style={{
          marginTop: '20px',
          padding: '15px',
          backgroundColor: '#d4edda',
          border: '1px solid #c3e6cb',
          borderRadius: '4px'
        }}>
          <h4 style={{ margin: '0 0 10px 0', color: '#155724' }}>
            ✅ Analysis Complete
          </h4>
          <p style={{ margin: 0, color: '#155724' }}>
            E-tray analysis completed successfully. Check the results table below for detailed output.
          </p>
        </div>
      )}

      {lastResult && lastResult.status === 'error' && (
        <div style={{
          marginTop: '20px',
          padding: '15px',
          backgroundColor: '#f8d7da',
          border: '1px solid #f5c6cb',
          borderRadius: '4px'
        }}>
          <h4 style={{ margin: '0 0 10px 0', color: '#721c24' }}>
            ❌ Analysis Failed
          </h4>
          <p style={{ margin: 0, color: '#721c24' }}>
            There was an error processing your E-tray analysis. Please try again or check your input data.
          </p>
        </div>
      )}

      {/* Modals */}
      <ContentModal
        isOpen={modalState.isOpen}
        onClose={closeModal}
        title={modalState.title}
        content={modalState.content}
      />

      <PromptModal
        isOpen={promptModalState.isOpen}
        onClose={closePromptModal}
        promptName={promptModalState.promptName}
        promptData={promptModalState.promptData}
        onSave={(newContent) => handlePromptUpdate(promptModalState.promptId, newContent)}
      />
    </div>
  );
};

export default EtrayEvaluationRunner;
