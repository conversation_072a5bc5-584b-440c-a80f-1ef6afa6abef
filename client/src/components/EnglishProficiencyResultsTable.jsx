import React, { useState } from 'react';
import AnnotationSelector from './AnnotationSelector';
import PromptModal from './PromptModal';
import { englishProficiencyEvaluationsApi } from '../services/api';

const EnglishProficiencyResultsTable = ({ evaluations, onEvaluationUpdate }) => {
  const [expandedRow, setExpandedRow] = useState(null);
  const [expandedCriteria, setExpandedCriteria] = useState({});
  const [modalState, setModalState] = useState({
    isOpen: false,
    promptData: null,
    promptName: '',
    loading: false
  });

  const toggleRow = (id) => {
    setExpandedRow(expandedRow === id ? null : id);
  };

  const toggleCriteria = (evaluationId, criteriaName) => {
    const key = `${evaluationId}-${criteriaName}`;
    setExpandedCriteria(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  const handleAnnotationUpdate = async (evaluationId, annotation) => {
    try {
      const response = await englishProficiencyEvaluationsApi.updateAnnotation(evaluationId, annotation);
      onEvaluationUpdate(response.data);
    } catch (error) {
      console.error('Failed to update annotation:', error);
      throw error;
    }
  };

  const handleViewPrompt = async (promptVersion, promptContent, promptName) => {
    setModalState({
      isOpen: true,
      promptData: promptContent,
      promptName: promptName,
      loading: false
    });
  };

  const closeModal = () => {
    setModalState({
      isOpen: false,
      promptData: null,
      promptName: '',
      loading: false
    });
  };

  const renderEvaluationOutput = (evaluation) => {
    if (!evaluation.output || !evaluation.output.evaluations) {
      return (
        <div style={{ color: '#666', fontStyle: 'italic' }}>
          No evaluation results available
        </div>
      );
    }

    const evaluations = evaluation.output.evaluations;
    const competencies = ['Fluency & Coherence', 'Vocabulary', 'Grammar', 'Pronunciation'];

    return (
      <div>
        <h4 style={{ margin: '0 0 16px 0', color: '#333' }}>📊 English Proficiency Analysis</h4>
        
        {/* Overall Scores Summary */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '12px',
          marginBottom: '20px'
        }}>
          {competencies.map(competency => {
            const score = evaluations[competency];
            return (
              <div key={competency} style={{
                padding: '12px',
                backgroundColor: '#f8f9fa',
                border: '1px solid #dee2e6',
                borderRadius: '6px',
                textAlign: 'center'
              }}>
                <div style={{ fontSize: '12px', color: '#666', marginBottom: '4px' }}>
                  {competency}
                </div>
                <div style={{
                  fontSize: '20px',
                  fontWeight: 'bold',
                  color: score >= 4 ? '#28a745' : score >= 3 ? '#ffc107' : '#dc3545'
                }}>
                  {typeof score === 'number' ? score.toFixed(1) : score || 'N/A'}
                </div>
              </div>
            );
          })}
        </div>

        {/* Detailed Analysis */}
        {evaluations.list_analysis && (
          <div style={{ marginBottom: '20px' }}>
            <h5 style={{ margin: '0 0 12px 0', color: '#555' }}>📋 Detailed Analysis</h5>
            {competencies.map(competency => {
              const analysisData = evaluations.list_analysis[competency];
              if (!analysisData || !Array.isArray(analysisData) || analysisData.length === 0) {
                return null;
              }

              return (
                <div key={competency} style={{ marginBottom: '16px' }}>
                  <div
                    onClick={() => toggleCriteria(evaluation.id, competency)}
                    style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      padding: '8px 12px',
                      backgroundColor: '#f8f9fa',
                      border: '1px solid #dee2e6',
                      borderRadius: '4px',
                      cursor: 'pointer',
                      marginBottom: '8px'
                    }}
                  >
                    <span style={{ fontWeight: '500' }}>{competency}</span>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      <span style={{
                        padding: '2px 8px',
                        backgroundColor: evaluations[competency] >= 4 ? '#d4edda' : evaluations[competency] >= 3 ? '#fff3cd' : '#f8d7da',
                        color: evaluations[competency] >= 4 ? '#155724' : evaluations[competency] >= 3 ? '#856404' : '#721c24',
                        borderRadius: '12px',
                        fontSize: '12px',
                        fontWeight: 'bold'
                      }}>
                        {typeof evaluations[competency] === 'number' ? evaluations[competency].toFixed(1) : evaluations[competency]}
                      </span>
                      <span style={{ fontSize: '12px', color: '#666' }}>
                        {expandedCriteria[`${evaluation.id}-${competency}`] ? '▼' : '▶'}
                      </span>
                    </div>
                  </div>

                  {expandedCriteria[`${evaluation.id}-${competency}`] && (
                    <div style={{
                      padding: '12px',
                      backgroundColor: '#ffffff',
                      border: '1px solid #dee2e6',
                      borderRadius: '4px',
                      fontSize: '14px'
                    }}>
                      {evaluations.list_analysis_summary && evaluations.list_analysis_summary[competency] && (
                        <div style={{ marginBottom: '12px' }}>
                          <strong>Summary:</strong>
                          <p style={{ margin: '8px 0 0 0', lineHeight: '1.4' }}>{evaluations.list_analysis_summary[competency]}</p>
                        </div>
                      )}
                      <strong>Analysis Points:</strong>
                      <ul style={{ margin: '8px 0 0 0', paddingLeft: '20px' }}>
                        {analysisData.map((item, index) => (
                          <li key={index} style={{ marginBottom: '4px', lineHeight: '1.4' }}>{item}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        )}

        {/* Summary */}
        {evaluations.summary && (
          <div style={{
            padding: '16px',
            backgroundColor: '#e3f2fd',
            border: '1px solid #90caf9',
            borderRadius: '6px',
            marginBottom: '20px'
          }}>
            <h5 style={{ margin: '0 0 8px 0', color: '#1565c0' }}>📝 Summary</h5>
            <p style={{ margin: 0, color: '#1565c0', lineHeight: '1.5' }}>
              {evaluations.summary}
            </p>
          </div>
        )}

        {/* Transcriptions */}
        {evaluation.output.transcriptions && evaluation.output.transcriptions.length > 0 && (
          <div style={{ marginTop: '20px' }}>
            <h5 style={{ margin: '0 0 12px 0', color: '#555' }}>🎤 Phonetic Transcriptions</h5>
            {evaluation.output.transcriptions.map((item, index) => (
              <div key={index} style={{
                padding: '12px',
                backgroundColor: '#f8f9fa',
                border: '1px solid #dee2e6',
                borderRadius: '4px',
                marginBottom: '8px'
              }}>
                <div style={{ fontWeight: '500', marginBottom: '4px', color: '#333' }}>
                  Question {index + 1}: {item.question}
                </div>
                <div style={{ fontSize: '14px', color: '#666', fontFamily: 'monospace' }}>
                  <strong>IPA Transcript:</strong> {item.transcript}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    );
  };

  if (!evaluations || evaluations.length === 0) {
    return (
      <div style={{
        backgroundColor: 'white',
        border: '1px solid #ddd',
        borderRadius: '8px',
        padding: '40px',
        textAlign: 'center',
        color: '#666'
      }}>
        <h3>No English Proficiency evaluations yet</h3>
        <p>Run your first evaluation to see results here.</p>
      </div>
    );
  }

  return (
    <div style={{
      backgroundColor: 'white',
      border: '1px solid #ddd',
      borderRadius: '8px',
      padding: '24px'
    }}>
      <h3 style={{ marginTop: 0, marginBottom: '20px' }}>
        🗣️ English Proficiency Evaluation Results
      </h3>

      <PromptModal
        isOpen={modalState.isOpen}
        onClose={closeModal}
        promptData={modalState.promptData}
        promptName={modalState.promptName}
        loading={modalState.loading}
        readOnly={true}
      />

      <div style={{ overflow: 'auto' }}>
        <table style={{
          width: '100%',
          borderCollapse: 'collapse',
          fontSize: '14px'
        }}>
          <thead>
            <tr style={{ backgroundColor: '#f8f9fa' }}>
              <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #ddd' }}>
                Timestamp
              </th>
              <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #ddd' }}>
                Prompt Versions
              </th>
              <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #ddd' }}>
                Models & Temperature
              </th>
              <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #ddd' }}>
                Dataset
              </th>
              <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #ddd' }}>
                Status
              </th>
              <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #ddd' }}>
                Output
              </th>
              <th style={{ padding: '12px', textAlign: 'center', borderBottom: '1px solid #ddd' }}>
                Annotation
              </th>
              <th style={{ padding: '12px', textAlign: 'center', borderBottom: '1px solid #ddd' }}>
                Actions
              </th>
            </tr>
          </thead>
          <tbody>
            {evaluations.map((evaluation) => (
              <React.Fragment key={evaluation.id}>
                <tr style={{ borderBottom: '1px solid #eee' }}>
                  <td style={{ padding: '12px', verticalAlign: 'top' }}>
                    {new Date(evaluation.timestamp).toLocaleString()}
                  </td>
                  <td style={{ padding: '12px', verticalAlign: 'top' }}>
                    <div style={{ fontSize: '12px' }}>
                      <div style={{ marginBottom: '4px' }}>
                        <button
                          onClick={() => handleViewPrompt(
                            evaluation.prompt1Version,
                            evaluation.prompt1Content,
                            'English Proficiency Transcription Prompt'
                          )}
                          style={{
                            padding: '2px 6px',
                            fontSize: '11px',
                            border: '1px solid #007bff',
                            borderRadius: '3px',
                            backgroundColor: 'white',
                            color: '#007bff',
                            cursor: 'pointer',
                            textDecoration: 'none'
                          }}
                        >
                          Transcription: v{evaluation.prompt1Version}
                        </button>
                      </div>
                      <div>
                        <button
                          onClick={() => handleViewPrompt(
                            evaluation.prompt2Version,
                            evaluation.prompt2Content,
                            'English Proficiency Analysis Prompt'
                          )}
                          style={{
                            padding: '2px 6px',
                            fontSize: '11px',
                            border: '1px solid #007bff',
                            borderRadius: '3px',
                            backgroundColor: 'white',
                            color: '#007bff',
                            cursor: 'pointer',
                            textDecoration: 'none'
                          }}
                        >
                          Analysis: v{evaluation.prompt2Version}
                        </button>
                      </div>
                    </div>
                  </td>
                  <td style={{ padding: '12px', verticalAlign: 'top' }}>
                    {evaluation.details?.models && evaluation.details?.temperatures ? (
                      <div style={{ fontSize: '12px' }}>
                        <div><strong>Transcribe:</strong> {evaluation.details.models.transcribe} (T: {evaluation.details.temperatures.transcribe})</div>
                        <div><strong>Analysis:</strong> {evaluation.details.models.analysis} (T: {evaluation.details.temperatures.analysis})</div>
                      </div>
                    ) : (
                      <span style={{ color: '#666', fontSize: '12px' }}>N/A</span>
                    )}
                  </td>
                  <td style={{ padding: '12px', verticalAlign: 'top' }}>
                    <div style={{ fontSize: '12px' }}>
                      <div><strong>Name:</strong> {evaluation.dataset_name}</div>
                      <div><strong>ID:</strong> {evaluation.dataset_id}</div>
                    </div>
                  </td>
                  <td style={{ padding: '12px', verticalAlign: 'top' }}>
                    <span style={{
                      padding: '4px 8px',
                      borderRadius: '12px',
                      fontSize: '12px',
                      fontWeight: 'bold',
                      backgroundColor: 
                        evaluation.status === 'completed' ? '#d4edda' :
                        evaluation.status === 'error' ? '#f8d7da' : '#fff3cd',
                      color: 
                        evaluation.status === 'completed' ? '#155724' :
                        evaluation.status === 'error' ? '#721c24' : '#856404'
                    }}>
                      {evaluation.status === 'completed' ? '✅ Completed' :
                       evaluation.status === 'error' ? '❌ Error' : '⏳ Processing'}
                    </span>
                  </td>
                  <td style={{ padding: '12px', verticalAlign: 'top' }}>
                    {evaluation.status === 'completed' && evaluation.output?.evaluations ? (
                      <div style={{ fontSize: '12px' }}>
                        <div><strong>Fluency:</strong> {evaluation.output.evaluations['Fluency & Coherence'] || 'N/A'}</div>
                        <div><strong>Vocabulary:</strong> {evaluation.output.evaluations['Vocabulary'] || 'N/A'}</div>
                        <div><strong>Grammar:</strong> {evaluation.output.evaluations['Grammar'] || 'N/A'}</div>
                        <div><strong>Pronunciation:</strong> {evaluation.output.evaluations['Pronunciation'] || 'N/A'}</div>
                      </div>
                    ) : evaluation.status === 'error' ? (
                      <span style={{ color: '#dc3545', fontSize: '12px' }}>Error occurred</span>
                    ) : (
                      <span style={{ color: '#666', fontSize: '12px' }}>Processing...</span>
                    )}
                  </td>
                  <td style={{ padding: '12px', textAlign: 'center', verticalAlign: 'top' }}>
                    <AnnotationSelector
                      evaluationId={evaluation.id}
                      currentAnnotation={evaluation.annotation}
                      onAnnotationUpdate={handleAnnotationUpdate}
                    />
                  </td>
                  <td style={{ padding: '12px', textAlign: 'center', verticalAlign: 'top' }}>
                    <button
                      onClick={() => toggleRow(evaluation.id)}
                      style={{
                        padding: '4px 8px',
                        border: '1px solid #007bff',
                        borderRadius: '4px',
                        backgroundColor: 'white',
                        color: '#007bff',
                        cursor: 'pointer',
                        fontSize: '12px'
                      }}
                    >
                      {expandedRow === evaluation.id ? 'Hide Details' : 'View Details'}
                    </button>
                  </td>
                </tr>

                {expandedRow === evaluation.id && (
                  <tr>
                    <td colSpan="8" style={{ padding: '20px', backgroundColor: '#f8f9fa' }}>
                      {evaluation.status === 'completed' ? (
                        renderEvaluationOutput(evaluation)
                      ) : evaluation.status === 'error' ? (
                        <div style={{ color: '#dc3545' }}>
                          <h4>❌ Error Details</h4>
                          <pre style={{ 
                            backgroundColor: '#f8d7da', 
                            padding: '10px', 
                            borderRadius: '4px',
                            fontSize: '12px',
                            overflow: 'auto'
                          }}>
                            {evaluation.details?.error || 'Unknown error occurred'}
                          </pre>
                        </div>
                      ) : (
                        <div style={{ textAlign: 'center', color: '#6c757d' }}>
                          <h4>⏳ Processing in Progress</h4>
                          <p>This evaluation is currently being processed. Results will appear here when complete.</p>
                        </div>
                      )}
                    </td>
                  </tr>
                )}
              </React.Fragment>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default EnglishProficiencyResultsTable;
