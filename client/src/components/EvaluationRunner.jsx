import React, { useState } from 'react';

const CompetencyGapsInput = ({ value, onChange }) => {
  const [competencies, setCompetencies] = useState(value || [{ name: '', gap: '' }]);

  const handleCompetencyChange = (index, field, newValue) => {
    const updated = [...competencies];
    updated[index][field] = newValue;
    setCompetencies(updated);

    // Format as array of objects for the new prompt structure
    const formatted = updated
      .filter(comp => comp.name.trim() && comp.gap.trim())
      .map((comp, i) => ({
        user_id: i + 1,
        competency_name: comp.name.trim(),
        gap_percentage: parseFloat(comp.gap.trim()) || 0
      }));
    onChange(formatted);
  };

  const addNewRow = () => {
    setCompetencies([...competencies, { name: '', gap: '' }]);
  };

  const removeRow = (index) => {
    if (competencies.length > 1) {
      const updated = competencies.filter((_, i) => i !== index);
      setCompetencies(updated);

      // Update parent with formatted data
      const formatted = updated
        .filter(comp => comp.name.trim() && comp.gap.trim())
        .map((comp, i) => ({
          user_id: i + 1,
          competency_name: comp.name.trim(),
          gap_percentage: parseFloat(comp.gap.trim()) || 0
        }));
      onChange(formatted);
    }
  };

  return (
    <div>
      <label style={{
        display: 'block',
        marginBottom: '8px',
        fontWeight: 'bold'
      }}>
        Competency Gaps:
      </label>
      <div style={{ marginBottom: '8px', fontSize: '12px', color: '#666' }}>
        <strong>competency name:</strong> gap percent
      </div>

      {competencies.map((competency, index) => (
        <div key={index} style={{
          display: 'flex',
          gap: '8px',
          marginBottom: '8px',
          alignItems: 'center'
        }}>
          <input
            type="text"
            placeholder="Competency name"
            value={competency.name}
            onChange={(e) => handleCompetencyChange(index, 'name', e.target.value)}
            style={{
              flex: 2,
              padding: '6px',
              border: '1px solid #ccc',
              borderRadius: '4px',
              fontSize: '14px'
            }}
          />
          <input
            type="number"
            placeholder="0-100"
            min="0"
            max="100"
            step="0.01"
            value={competency.gap}
            onChange={(e) => handleCompetencyChange(index, 'gap', e.target.value)}
            style={{
              flex: 1,
              padding: '6px',
              border: '1px solid #ccc',
              borderRadius: '4px',
              fontSize: '14px'
            }}
          />
          {competencies.length > 1 && (
            <button
              type="button"
              onClick={() => removeRow(index)}
              style={{
                padding: '6px 8px',
                border: '1px solid #dc3545',
                borderRadius: '4px',
                backgroundColor: '#dc3545',
                color: 'white',
                cursor: 'pointer',
                fontSize: '12px'
              }}
            >
              ×
            </button>
          )}
        </div>
      ))}

      <button
        type="button"
        onClick={addNewRow}
        style={{
          padding: '6px 12px',
          border: '1px solid #28a745',
          borderRadius: '4px',
          backgroundColor: '#28a745',
          color: 'white',
          cursor: 'pointer',
          fontSize: '14px',
          marginTop: '4px'
        }}
      >
        + Add new row
      </button>
    </div>
  );
};

const EvaluationRunner = ({ onRun }) => {
  const [roleName, setRoleName] = useState('');
  const [competencyGaps, setCompetencyGaps] = useState([]);
  const [running, setRunning] = useState(false);
  const [lastResult, setLastResult] = useState(null);

  const handleRun = async () => {
    if (!roleName.trim()) {
      alert('Please enter a role name');
      return;
    }
    if (!competencyGaps || competencyGaps.length === 0) {
      alert('Please enter at least one competency gap');
      return;
    }

    try {
      setRunning(true);
      const result = await onRun({
        roleName: roleName.trim(),
        competencyGaps
      });
      setLastResult(result);
    } catch (error) {
      alert('Failed to run evaluation');
    } finally {
      setRunning(false);
    }
  };

  return (
    <div style={{
      border: '1px solid #ddd',
      borderRadius: '8px',
      padding: '16px',
      backgroundColor: 'white'
    }}>
      <h3 style={{ marginTop: 0 }}>Run Evaluation</h3>

      {/* Role Name Input */}
      <div style={{ marginBottom: '16px' }}>
        <label style={{
          display: 'block',
          marginBottom: '8px',
          fontWeight: 'bold'
        }}>
          Role Name:
        </label>
        <input
          type="text"
          placeholder="e.g., Project Manager, Software Developer, etc."
          value={roleName}
          onChange={(e) => setRoleName(e.target.value)}
          style={{
            width: '100%',
            padding: '8px',
            border: '1px solid #ccc',
            borderRadius: '4px',
            fontSize: '14px'
          }}
        />
      </div>

      {/* Competency Gaps Input */}
      <div style={{ marginBottom: '16px' }}>
        <CompetencyGapsInput
          value={competencyGaps}
          onChange={setCompetencyGaps}
        />
      </div>

      <button
        onClick={handleRun}
        disabled={running || !roleName.trim() || !competencyGaps || competencyGaps.length === 0}
        style={{
          padding: '10px 20px',
          border: 'none',
          borderRadius: '4px',
          backgroundColor: running ? '#6c757d' : '#28a745',
          color: 'white',
          cursor: running ? 'not-allowed' : 'pointer',
          fontSize: '16px',
          fontWeight: 'bold'
        }}
      >
        {running ? 'Running...' : 'Run Prompt Chain'}
      </button>

      {lastResult && (
        <div style={{
          marginTop: '16px',
          padding: '12px',
          backgroundColor: '#d4edda',
          border: '1px solid #c3e6cb',
          borderRadius: '4px'
        }}>
          <h4 style={{ margin: '0 0 8px 0', color: '#155724' }}>
            Latest Result:
          </h4>
          <div style={{
            fontSize: '14px',
            fontFamily: 'monospace',
            whiteSpace: 'pre-wrap',
            maxHeight: '200px',
            overflow: 'auto'
          }}>
            {lastResult.output}
          </div>
          <div style={{
            fontSize: '12px',
            color: '#666',
            marginTop: '8px'
          }}>
            Completed at: {new Date(lastResult.timestamp).toLocaleString()}
          </div>
        </div>
      )}
    </div>
  );
};

export default EvaluationRunner;
