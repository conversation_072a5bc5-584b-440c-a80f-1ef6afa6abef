import React, { useState } from 'react';
import { aiInterviewV2EvaluationsApi, promptsApi } from '../services/api';

// Annotation selector component
const AnnotationSelector = ({ evaluationId, currentAnnotation, onAnnotationUpdate }) => {
  const [updating, setUpdating] = useState(false);

  const handleAnnotationChange = async (annotation) => {
    try {
      setUpdating(true);
      const response = await aiInterviewV2EvaluationsApi.updateAnnotation(evaluationId, annotation);
      onAnnotationUpdate(response.data);
    } catch (error) {
      console.error('Error updating annotation:', error);
      alert('Failed to update annotation');
    } finally {
      setUpdating(false);
    }
  };

  return (
    <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
      <button
        onClick={() => handleAnnotationChange('good')}
        disabled={updating}
        style={{
          padding: '4px 8px',
          border: '1px solid #28a745',
          borderRadius: '4px',
          backgroundColor: currentAnnotation === 'good' ? '#28a745' : 'white',
          color: currentAnnotation === 'good' ? 'white' : '#28a745',
          cursor: updating ? 'not-allowed' : 'pointer',
          fontSize: '12px'
        }}
      >
        👍 Good
      </button>
      <button
        onClick={() => handleAnnotationChange('not good')}
        disabled={updating}
        style={{
          padding: '4px 8px',
          border: '1px solid #dc3545',
          borderRadius: '4px',
          backgroundColor: currentAnnotation === 'not good' ? '#dc3545' : 'white',
          color: currentAnnotation === 'not good' ? 'white' : '#dc3545',
          cursor: updating ? 'not-allowed' : 'pointer',
          fontSize: '12px'
        }}
      >
        👎 Not Good
      </button>
      {currentAnnotation && (
        <button
          onClick={() => handleAnnotationChange(null)}
          disabled={updating}
          style={{
            padding: '4px 8px',
            border: '1px solid #6c757d',
            borderRadius: '4px',
            backgroundColor: 'white',
            color: '#6c757d',
            cursor: updating ? 'not-allowed' : 'pointer',
            fontSize: '12px'
          }}
        >
          Clear
        </button>
      )}
    </div>
  );
};

// Prompt Modal Component
const PromptModal = ({ isOpen, onClose, promptData, promptName, loading }) => {
  if (!isOpen) return null;

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 1000
    }}>
      <div style={{
        backgroundColor: 'white',
        borderRadius: '8px',
        padding: '20px',
        maxWidth: '800px',
        maxHeight: '768px',
        width: '90%',
        overflow: 'auto'
      }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '20px'
        }}>
          <h3 style={{ margin: 0 }}>{promptName}</h3>
          <button
            onClick={onClose}
            style={{
              background: 'none',
              border: 'none',
              fontSize: '24px',
              cursor: 'pointer',
              color: '#6c757d'
            }}
          >
            ×
          </button>
        </div>
        
        {loading ? (
          <div style={{ textAlign: 'center', padding: '20px' }}>
            Loading prompt...
          </div>
        ) : (
          <div style={{
            backgroundColor: '#f8f9fa',
            padding: '15px',
            borderRadius: '4px',
            border: '1px solid #e9ecef',
            maxHeight: '600px',
            overflow: 'auto'
          }}>
            <pre style={{
              whiteSpace: 'pre-wrap',
              wordWrap: 'break-word',
              margin: 0,
              fontSize: '14px',
              lineHeight: '1.5'
            }}>
              {promptData}
            </pre>
          </div>
        )}
      </div>
    </div>
  );
};

const AIInterviewV2ResultsTable = ({ evaluations, onEvaluationUpdate }) => {
  const [expandedRow, setExpandedRow] = useState(null);
  const [expandedCriteria, setExpandedCriteria] = useState({});
  const [modalState, setModalState] = useState({
    isOpen: false,
    promptData: null,
    promptName: '',
    loading: false
  });

  const toggleRow = (id) => {
    setExpandedRow(expandedRow === id ? null : id);
  };

  const toggleCriteria = (evaluationId, criteriaName) => {
    const key = `${evaluationId}-${criteriaName}`;
    setExpandedCriteria(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  const handleAnnotationUpdate = (updatedEvaluation) => {
    onEvaluationUpdate(updatedEvaluation);
  };

  const showPrompt = async (promptId, promptName) => {
    setModalState({
      isOpen: true,
      promptData: null,
      promptName: promptName,
      loading: true
    });

    try {
      const response = await promptsApi.getById(promptId);
      setModalState(prev => ({
        ...prev,
        promptData: response.data.content,
        loading: false
      }));
    } catch (error) {
      console.error('Error fetching prompt:', error);
      setModalState(prev => ({
        ...prev,
        promptData: 'Error loading prompt',
        loading: false
      }));
    }
  };

  const closeModal = () => {
    setModalState({
      isOpen: false,
      promptData: null,
      promptName: '',
      loading: false
    });
  };

  const getStatusBadge = (status) => {
    const styles = {
      'in_progress': { backgroundColor: '#ffc107', color: '#212529' },
      'completed': { backgroundColor: '#28a745', color: 'white' },
      'error': { backgroundColor: '#dc3545', color: 'white' }
    };

    return (
      <span style={{
        padding: '4px 8px',
        borderRadius: '12px',
        fontSize: '12px',
        fontWeight: '500',
        ...styles[status]
      }}>
        {status === 'in_progress' ? '⏳ In Progress' :
         status === 'completed' ? '✅ Completed' :
         '❌ Error'}
      </span>
    );
  };

  const renderEvaluationOutput = (evaluation) => {
    if (!evaluation.output || !evaluation.output.evaluations) {
      return <div style={{ color: '#6c757d', fontStyle: 'italic' }}>No evaluation results available</div>;
    }

    const evaluations_data = evaluation.output.evaluations;
    const criteriaNames = Object.keys(evaluations_data).filter(key => 
      key !== 'list_analysis' && key !== 'summary'
    );

    return (
      <div style={{ marginTop: '15px' }}>
        <h4 style={{ marginBottom: '15px', color: '#495057' }}>📊 Evaluation Results</h4>
        
        {/* Criteria Scores */}
        <div style={{ marginBottom: '20px' }}>
          <h5 style={{ marginBottom: '10px', color: '#6c757d' }}>Competency Scores:</h5>
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '10px' }}>
            {criteriaNames.map(criteria => (
              <div key={criteria} style={{
                padding: '10px',
                backgroundColor: '#f8f9fa',
                borderRadius: '6px',
                border: '1px solid #e9ecef'
              }}>
                <div style={{ fontWeight: '500', marginBottom: '5px' }}>{criteria}</div>
                <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#007bff' }}>
                  {typeof evaluations_data[criteria] === 'number' ? 
                    evaluations_data[criteria].toFixed(1) : 
                    evaluations_data[criteria]}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* List Analysis */}
        {evaluations_data.list_analysis && (
          <div style={{ marginBottom: '20px' }}>
            <h5 style={{ marginBottom: '10px', color: '#6c757d' }}>Question Analysis:</h5>
            {Object.entries(evaluations_data.list_analysis).map(([criteria, analyses]) => (
              <div key={criteria} style={{ marginBottom: '15px' }}>
                <button
                  onClick={() => toggleCriteria(evaluation.id, criteria)}
                  style={{
                    background: 'none',
                    border: '1px solid #dee2e6',
                    borderRadius: '4px',
                    padding: '8px 12px',
                    cursor: 'pointer',
                    width: '100%',
                    textAlign: 'left',
                    fontWeight: '500',
                    backgroundColor: '#f8f9fa'
                  }}
                >
                  {expandedCriteria[`${evaluation.id}-${criteria}`] ? '▼' : '▶'} {criteria}
                </button>
                
                {expandedCriteria[`${evaluation.id}-${criteria}`] && (
                  <div style={{
                    marginTop: '10px',
                    padding: '15px',
                    backgroundColor: 'white',
                    border: '1px solid #e9ecef',
                    borderRadius: '4px'
                  }}>
                    {Array.isArray(analyses) ? (
                      <ul style={{ margin: 0, paddingLeft: '20px' }}>
                        {analyses.map((analysis, index) => (
                          <li key={index} style={{ marginBottom: '5px', fontSize: '14px' }}>
                            {analysis}
                          </li>
                        ))}
                      </ul>
                    ) : (
                      <div style={{ fontSize: '14px' }}>{analyses}</div>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}

        {/* Summary */}
        {evaluations_data.summary && (
          <div>
            <h5 style={{ marginBottom: '10px', color: '#6c757d' }}>Summary:</h5>
            <div style={{
              padding: '15px',
              backgroundColor: '#f8f9fa',
              borderRadius: '6px',
              border: '1px solid #e9ecef',
              fontSize: '14px',
              lineHeight: '1.6'
            }}>
              {evaluations_data.summary}
            </div>
          </div>
        )}
      </div>
    );
  };

  if (!evaluations || evaluations.length === 0) {
    return (
      <div style={{
        textAlign: 'center',
        padding: '40px',
        color: '#6c757d',
        backgroundColor: '#f8f9fa',
        borderRadius: '8px',
        border: '1px solid #dee2e6'
      }}>
        <h3>No AI Interview V2 evaluations yet</h3>
        <p>Run your first AI Interview V2 evaluation to see results here.</p>
      </div>
    );
  }

  return (
    <div>
      <h2 style={{ marginBottom: '20px' }}>🎤 AI Interview V2 Results</h2>
      
      <PromptModal
        isOpen={modalState.isOpen}
        onClose={closeModal}
        promptData={modalState.promptData}
        promptName={modalState.promptName}
        loading={modalState.loading}
      />

      <div style={{ overflow: 'auto' }}>
        <table style={{
          width: '100%',
          borderCollapse: 'collapse',
          fontSize: '14px'
        }}>
          <thead>
            <tr style={{ backgroundColor: '#f8f9fa' }}>
              <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #ddd' }}>
                Timestamp
              </th>
              <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #ddd' }}>
                Prompt Versions
              </th>
              <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #ddd' }}>
                Models & Temperature
              </th>
              <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #ddd' }}>
                Dataset
              </th>
              <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #ddd' }}>
                Status
              </th>
              <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #ddd' }}>
                Output
              </th>
              <th style={{ padding: '12px', textAlign: 'center', borderBottom: '1px solid #ddd' }}>
                Annotation
              </th>
            </tr>
          </thead>
          <tbody>
            {evaluations.map((evaluation) => (
              <React.Fragment key={evaluation.id}>
                <tr
                  style={{
                    borderBottom: '1px solid #ddd',
                    backgroundColor: expandedRow === evaluation.id ? '#f8f9fa' : 'white',
                    cursor: 'pointer'
                  }}
                  onClick={() => toggleRow(evaluation.id)}
                >
                  <td style={{ padding: '12px' }}>
                    {new Date(evaluation.timestamp).toLocaleString()}
                  </td>
                  <td style={{ padding: '12px' }}>
                    <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          showPrompt(12, 'AI Interview V2 Transcription Prompt');
                        }}
                        style={{
                          background: 'none',
                          border: '1px solid #007bff',
                          borderRadius: '4px',
                          padding: '2px 6px',
                          cursor: 'pointer',
                          fontSize: '12px',
                          color: '#007bff'
                        }}
                      >
                        📝 Transcription v{evaluation.prompt1Version}
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          showPrompt(13, 'AI Interview V2 Analysis Prompt');
                        }}
                        style={{
                          background: 'none',
                          border: '1px solid #007bff',
                          borderRadius: '4px',
                          padding: '2px 6px',
                          cursor: 'pointer',
                          fontSize: '12px',
                          color: '#007bff'
                        }}
                      >
                        🔍 Analysis v{evaluation.prompt2Version}
                      </button>
                    </div>
                  </td>
                  <td style={{ padding: '12px' }}>
                    {evaluation.details?.models && evaluation.details?.temperatures ? (
                      <div style={{ fontSize: '12px' }}>
                        <div><strong>Transcribe:</strong> {evaluation.details.models.transcribe} (T: {evaluation.details.temperatures.transcribe})</div>
                        <div><strong>Analysis:</strong> {evaluation.details.models.analysis} (T: {evaluation.details.temperatures.analysis})</div>
                      </div>
                    ) : (
                      <span style={{ color: '#6c757d', fontStyle: 'italic' }}>N/A</span>
                    )}
                  </td>
                  <td style={{ padding: '12px' }}>
                    <div style={{ fontWeight: '500' }}>{evaluation.dataset_name}</div>
                  </td>
                  <td style={{ padding: '12px' }}>
                    {getStatusBadge(evaluation.status)}
                  </td>
                  <td style={{ padding: '12px' }}>
                    {evaluation.status === 'completed' ? (
                      <span style={{ color: '#28a745' }}>✅ Available</span>
                    ) : evaluation.status === 'error' ? (
                      <span style={{ color: '#dc3545' }}>❌ Failed</span>
                    ) : (
                      <span style={{ color: '#ffc107' }}>⏳ Processing</span>
                    )}
                  </td>
                  <td style={{ padding: '12px', textAlign: 'center' }}>
                    <AnnotationSelector
                      evaluationId={evaluation.id}
                      currentAnnotation={evaluation.annotation}
                      onAnnotationUpdate={handleAnnotationUpdate}
                    />
                  </td>
                </tr>
                
                {expandedRow === evaluation.id && (
                  <tr>
                    <td colSpan="7" style={{ padding: '20px', backgroundColor: '#f8f9fa' }}>
                      {evaluation.status === 'completed' ? (
                        renderEvaluationOutput(evaluation)
                      ) : evaluation.status === 'error' ? (
                        <div style={{ color: '#dc3545' }}>
                          <h4>❌ Error Details</h4>
                          <pre style={{ 
                            backgroundColor: '#f8d7da', 
                            padding: '10px', 
                            borderRadius: '4px',
                            fontSize: '12px',
                            overflow: 'auto'
                          }}>
                            {evaluation.details?.error || 'Unknown error occurred'}
                          </pre>
                        </div>
                      ) : (
                        <div style={{ textAlign: 'center', color: '#6c757d' }}>
                          <h4>⏳ Processing in Progress</h4>
                          <p>This evaluation is currently being processed. Results will appear here when complete.</p>
                        </div>
                      )}
                    </td>
                  </tr>
                )}
              </React.Fragment>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default AIInterviewV2ResultsTable;
