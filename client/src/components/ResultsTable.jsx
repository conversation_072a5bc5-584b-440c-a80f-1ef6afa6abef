import React, { useState } from 'react';
import PromptModal from './PromptModal';
import AnnotationSelector from './AnnotationSelector';
import { promptsApi, evaluationsApi } from '../services/api';

// Helper function to format the Final Output JSON into bulleted lists
const formatFinalOutput = (outputString) => {
  try {
    const parsed = JSON.parse(outputString);
    const sections = [];

    // Format task_challenges
    if (parsed.task_challenges && parsed.task_challenges.length > 0) {
      sections.push({
        title: 'Task Challenges:',
        items: parsed.task_challenges.map(item => item.task_challenge || item)
      });
    }

    // Format mentorships
    if (parsed.mentorships && parsed.mentorships.length > 0) {
      sections.push({
        title: 'Mentorships:',
        items: parsed.mentorships.map(item => item.mentorship || item)
      });
    }

    // Format training_workshops
    if (parsed.training_workshops && parsed.training_workshops.length > 0) {
      sections.push({
        title: 'Training Workshops:',
        items: parsed.training_workshops.map(item => item.training_workshop || item)
      });
    }

    return sections;
  } catch (error) {
    // If JSON parsing fails, return the original string
    return null;
  }
};

const ResultsTable = ({ evaluations, onEvaluationUpdate }) => {
  const [expandedRow, setExpandedRow] = useState(null);
  const [modalState, setModalState] = useState({
    isOpen: false,
    promptData: null,
    promptName: '',
    loading: false
  });

  const toggleRow = (id) => {
    setExpandedRow(expandedRow === id ? null : id);
  };

  const handlePromptVersionClick = async (promptId, version, promptName, evaluation = null) => {
    setModalState({
      isOpen: true,
      promptData: null,
      promptName,
      loading: true
    });

    try {
      // First try to use stored content from evaluation if available
      if (evaluation) {
        const storedContent = promptId === 1 ? evaluation.prompt1Content : evaluation.prompt2Content;
        if (storedContent) {
          setModalState(prev => ({
            ...prev,
            promptData: {
              content: storedContent,
              version: version,
              updatedAt: evaluation.timestamp
            },
            loading: false
          }));
          return;
        }
      }

      // Fallback to API call for historical versions
      const response = await promptsApi.getVersion(promptId, version);
      setModalState(prev => ({
        ...prev,
        promptData: response.data,
        loading: false
      }));
    } catch (error) {
      console.error('Error fetching prompt version:', error);
      setModalState(prev => ({
        ...prev,
        promptData: { content: 'Error loading prompt content', version, updatedAt: new Date().toISOString() },
        loading: false
      }));
    }
  };

  const closeModal = () => {
    setModalState({
      isOpen: false,
      promptData: null,
      promptName: '',
      loading: false
    });
  };

  const handleAnnotationUpdate = async (evaluationId, annotation) => {
    try {
      const response = await evaluationsApi.updateAnnotation(evaluationId, annotation);
      if (onEvaluationUpdate) {
        onEvaluationUpdate(response.data);
      }
    } catch (error) {
      console.error('Failed to update annotation:', error);
      throw error;
    }
  };

  if (evaluations.length === 0) {
    return (
      <div style={{
        border: '1px solid #ddd',
        borderRadius: '8px',
        padding: '20px',
        backgroundColor: 'white',
        textAlign: 'center'
      }}>
        <h3>Evaluation Results</h3>
        <p style={{ color: '#666' }}>No evaluations run yet. Use the form above to run your first evaluation.</p>
      </div>
    );
  }

  return (
    <div style={{
      border: '1px solid #ddd',
      borderRadius: '8px',
      backgroundColor: 'white',
      overflow: 'hidden'
    }}>
      <h3 style={{
        margin: 0,
        padding: '16px',
        backgroundColor: '#f8f9fa',
        borderBottom: '1px solid #ddd'
      }}>
        Evaluation Results ({evaluations.length})
      </h3>

      <div style={{ overflow: 'auto' }}>
        <table style={{
          width: '100%',
          borderCollapse: 'collapse',
          fontSize: '14px'
        }}>
          <thead>
            <tr style={{ backgroundColor: '#f8f9fa' }}>
              <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #ddd' }}>
                Timestamp
              </th>
              <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #ddd' }}>
                Prompt Versions
              </th>
              <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #ddd' }}>
                Input
              </th>
              <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #ddd' }}>
                Objectives
              </th>
              <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #ddd' }}>
                Output
              </th>
              <th style={{ padding: '12px', textAlign: 'center', borderBottom: '1px solid #ddd' }}>
                Annotation
              </th>
              <th style={{ padding: '12px', textAlign: 'center', borderBottom: '1px solid #ddd' }}>
                Actions
              </th>
            </tr>
          </thead>
          <tbody>
            {evaluations.map((evaluation) => (
              <React.Fragment key={evaluation.id}>
                <tr style={{
                  borderBottom: '1px solid #eee',
                  backgroundColor: expandedRow === evaluation.id ? '#f8f9fa' : 'white'
                }}>
                  <td style={{ padding: '12px', verticalAlign: 'top' }}>
                    {new Date(evaluation.timestamp).toLocaleString()}
                  </td>
                  <td style={{ padding: '12px', verticalAlign: 'top' }}>
                    <div>
                      P1: <button
                        onClick={() => handlePromptVersionClick(1, evaluation.prompt1Version, 'Prompt 1', evaluation)}
                        style={{
                          background: 'none',
                          border: 'none',
                          color: '#007bff',
                          textDecoration: 'underline',
                          cursor: 'pointer',
                          padding: 0,
                          font: 'inherit'
                        }}
                      >
                        v{evaluation.prompt1Version}
                      </button>
                    </div>
                    <div>
                      P2: <button
                        onClick={() => handlePromptVersionClick(2, evaluation.prompt2Version, 'Prompt 2', evaluation)}
                        style={{
                          background: 'none',
                          border: 'none',
                          color: '#007bff',
                          textDecoration: 'underline',
                          cursor: 'pointer',
                          padding: 0,
                          font: 'inherit'
                        }}
                      >
                        v{evaluation.prompt2Version}
                      </button>
                    </div>
                  </td>
                  <td style={{
                    padding: '12px',
                    verticalAlign: 'top',
                    maxWidth: '200px'
                  }}>
                    <div style={{
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap'
                    }}>
                      {evaluation.formattedInput || evaluation.input}
                    </div>
                  </td>
                  <td style={{
                    padding: '12px',
                    verticalAlign: 'top',
                    maxWidth: '200px'
                  }}>
                    <div style={{
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap'
                    }}>
                      {evaluation.objectives && evaluation.objectives.length > 0
                        ? `${evaluation.objectives.length} objective${evaluation.objectives.length > 1 ? 's' : ''} generated`
                        : 'No objectives'
                      }
                    </div>
                  </td>
                  <td style={{
                    padding: '12px',
                    verticalAlign: 'top',
                    maxWidth: '300px'
                  }}>
                    <div style={{
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap'
                    }}>
                      {evaluation.output}
                    </div>
                  </td>
                  <td style={{ padding: '12px', textAlign: 'center', verticalAlign: 'top' }}>
                    <AnnotationSelector
                      evaluationId={evaluation.id}
                      currentAnnotation={evaluation.annotation}
                      onAnnotationUpdate={handleAnnotationUpdate}
                    />
                  </td>
                  <td style={{ padding: '12px', textAlign: 'center', verticalAlign: 'top' }}>
                    <button
                      onClick={() => toggleRow(evaluation.id)}
                      style={{
                        padding: '4px 8px',
                        border: '1px solid #007bff',
                        borderRadius: '4px',
                        backgroundColor: 'white',
                        color: '#007bff',
                        cursor: 'pointer',
                        fontSize: '12px'
                      }}
                    >
                      {expandedRow === evaluation.id ? 'Collapse' : 'Expand'}
                    </button>
                  </td>
                </tr>
                {expandedRow === evaluation.id && (
                  <tr>
                    <td colSpan="7" style={{
                      padding: '16px',
                      backgroundColor: '#f8f9fa',
                      borderBottom: '1px solid #ddd'
                    }}>
                      <div style={{ display: 'grid', gap: '16px' }}>
                        <div>
                          <h4 style={{ margin: '0 0 8px 0' }}>Input:</h4>
                          <div style={{
                            padding: '8px',
                            backgroundColor: 'white',
                            border: '1px solid #ddd',
                            borderRadius: '4px',
                            fontFamily: 'monospace',
                            fontSize: '13px',
                            whiteSpace: 'pre-wrap'
                          }}>
                            {evaluation.formattedInput || evaluation.input}
                          </div>
                        </div>
                        {evaluation.objectives && evaluation.objectives.length > 0 && (
                          <div>
                            <h4 style={{ margin: '0 0 8px 0' }}>Generated Objectives:</h4>
                            <div style={{
                              padding: '8px',
                              backgroundColor: 'white',
                              border: '1px solid #ddd',
                              borderRadius: '4px',
                              fontFamily: 'monospace',
                              fontSize: '13px',
                              whiteSpace: 'pre-wrap'
                            }}>
                              {evaluation.objectives.map((objective, index) => (
                                `${index + 1}. ${objective}`
                              )).join('\n')}
                            </div>
                          </div>
                        )}
                        <div>
                          <h4 style={{ margin: '0 0 8px 0' }}>Final Output:</h4>
                          <div style={{
                            padding: '8px',
                            backgroundColor: 'white',
                            border: '1px solid #ddd',
                            borderRadius: '4px',
                            fontSize: '13px'
                          }}>
                            {(() => {
                              const formattedSections = formatFinalOutput(evaluation.output);

                              if (!formattedSections) {
                                // Fallback to original display if parsing fails
                                return (
                                  <div style={{
                                    fontFamily: 'monospace',
                                    whiteSpace: 'pre-wrap'
                                  }}>
                                    {evaluation.output}
                                  </div>
                                );
                              }

                              if (formattedSections.length === 0) {
                                return (
                                  <div style={{ color: '#666', fontStyle: 'italic' }}>
                                    No recommendations generated
                                  </div>
                                );
                              }

                              return (
                                <div>
                                  {formattedSections.map((section, sectionIndex) => (
                                    <div key={sectionIndex} style={{ marginBottom: sectionIndex < formattedSections.length - 1 ? '12px' : '0' }}>
                                      <div style={{
                                        fontWeight: 'bold',
                                        marginBottom: '4px',
                                        color: '#333'
                                      }}>
                                        {section.title}
                                      </div>
                                      <ul style={{
                                        margin: '0',
                                        paddingLeft: '20px',
                                        listStyleType: 'disc'
                                      }}>
                                        {section.items.map((item, itemIndex) => (
                                          <li key={itemIndex} style={{
                                            marginBottom: '4px',
                                            lineHeight: '1.4'
                                          }}>
                                            {item}
                                          </li>
                                        ))}
                                      </ul>
                                    </div>
                                  ))}
                                </div>
                              );
                            })()}
                          </div>
                        </div>
                      </div>
                    </td>
                  </tr>
                )}
              </React.Fragment>
            ))}
          </tbody>
        </table>
      </div>

      <PromptModal
        isOpen={modalState.isOpen}
        onClose={closeModal}
        promptData={modalState.promptData}
        promptName={modalState.promptName}
      />
    </div>
  );
};

export default ResultsTable;
