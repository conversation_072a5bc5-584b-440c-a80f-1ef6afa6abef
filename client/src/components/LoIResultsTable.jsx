import React, { useState } from 'react';
import AnnotationSelector from './AnnotationSelector';
import { loiEvaluationsApi } from '../services/api';

// Modal component for displaying input text
const InputTextModal = ({ isOpen, onClose, inputText }) => {
  if (!isOpen) return null;

  return (
    <div
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 10000
      }}
      onClick={onClose}
    >
      <div
        style={{
          backgroundColor: 'white',
          borderRadius: '8px',
          padding: '20px',
          maxWidth: '80%',
          maxHeight: '80vh',
          overflow: 'auto',
          position: 'relative'
        }}
        onClick={(e) => e.stopPropagation()}
      >
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '20px',
          borderBottom: '1px solid #eee',
          paddingBottom: '10px'
        }}>
          <h3 style={{ margin: 0 }}>Input Text</h3>
          <button
            onClick={onClose}
            style={{
              background: 'none',
              border: 'none',
              fontSize: '24px',
              cursor: 'pointer',
              color: '#666'
            }}
          >
            ×
          </button>
        </div>
        <div style={{
          fontSize: '14px',
          lineHeight: '1.6',
          whiteSpace: 'pre-wrap',
          wordBreak: 'break-word',
          maxHeight: '500px',
          overflowY: 'auto',
          padding: '10px',
          backgroundColor: '#f8f9fa',
          borderRadius: '4px'
        }}>
          {inputText}
        </div>
      </div>
    </div>
  );
};

const LoIResultsTable = ({ evaluations, onAnnotationUpdate }) => {
  const [expandedRow, setExpandedRow] = useState(null);
  const [inputModalState, setInputModalState] = useState({
    isOpen: false,
    inputText: ''
  });

  const toggleRow = (id) => {
    setExpandedRow(expandedRow === id ? null : id);
  };

  const openInputModal = (inputText) => {
    setInputModalState({
      isOpen: true,
      inputText
    });
  };

  const closeInputModal = () => {
    setInputModalState({
      isOpen: false,
      inputText: ''
    });
  };

  const getStatusDisplay = (status) => {
    switch (status) {
      case 'in_progress':
        return { text: 'Processing...', color: '#ffc107', bgColor: '#fff3cd' };
      case 'completed':
        return { text: 'Completed', color: '#28a745', bgColor: '#d4edda' };
      case 'error':
        return { text: 'Error', color: '#dc3545', bgColor: '#f8d7da' };
      default:
        return { text: status, color: '#6c757d', bgColor: '#e9ecef' };
    }
  };

  const formatAccuracy = (accuracy) => {
    if (accuracy === null || accuracy === undefined) return 'N/A';
    return `${(accuracy * 100).toFixed(1)}%`;
  };

  const getDiffColor = (diff) => {
    if (diff === 0) return '#28a745'; // Green for correct
    return '#dc3545'; // Red for incorrect
  };

  const getDiffBackgroundColor = (diff) => {
    if (diff === 0) return '#d4edda'; // Light green for correct
    return '#f8d7da'; // Light red for incorrect
  };

  const formatDiff = (diff) => {
    if (diff === 0) return '0';
    return diff > 0 ? `+${diff}` : `${diff}`;
  };

  if (evaluations.length === 0) {
    return (
      <div style={{
        border: '1px solid #ddd',
        borderRadius: '8px',
        padding: '20px',
        backgroundColor: 'white',
        textAlign: 'center'
      }}>
        <h3>LoI Scoring Evaluation Results</h3>
        <p style={{ color: '#666' }}>No evaluations run yet. Use the form above to run your first evaluation.</p>
      </div>
    );
  }

  return (
    <div style={{
      border: '1px solid #ddd',
      borderRadius: '8px',
      backgroundColor: 'white',
      overflow: 'hidden'
    }}>
      <h3 style={{
        margin: 0,
        padding: '16px',
        backgroundColor: '#f8f9fa',
        borderBottom: '1px solid #ddd'
      }}>
        LoI Scoring Evaluation Results ({evaluations.length})
      </h3>

      <div style={{ overflowX: 'auto' }}>
        <table style={{ width: '100%', borderCollapse: 'collapse' }}>
          <thead>
            <tr style={{ backgroundColor: '#f8f9fa' }}>
              <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #ddd' }}>
                Timestamp
              </th>
              <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #ddd' }}>
                Dataset
              </th>
              <th style={{ padding: '12px', textAlign: 'center', borderBottom: '1px solid #ddd' }}>
                Status
              </th>
              <th style={{ padding: '12px', textAlign: 'center', borderBottom: '1px solid #ddd' }}>
                Continuous Learning Accuracy
              </th>
              <th style={{ padding: '12px', textAlign: 'center', borderBottom: '1px solid #ddd' }}>
                Driving for Result Accuracy
              </th>
              <th style={{ padding: '12px', textAlign: 'center', borderBottom: '1px solid #ddd' }}>
                Rows Processed
              </th>
              <th style={{ padding: '12px', textAlign: 'center', borderBottom: '1px solid #ddd' }}>
                Annotation
              </th>
              <th style={{ padding: '12px', textAlign: 'center', borderBottom: '1px solid #ddd' }}>
                Actions
              </th>
            </tr>
          </thead>
          <tbody>
            {evaluations.map((evaluation) => (
              <React.Fragment key={evaluation.id}>
                <tr style={{
                  borderBottom: '1px solid #eee',
                  backgroundColor: expandedRow === evaluation.id ? '#f8f9fa' : 'white'
                }}>
                  <td style={{ padding: '12px', verticalAlign: 'top' }}>
                    {new Date(evaluation.timestamp).toLocaleString()}
                  </td>
                  <td style={{ padding: '12px', verticalAlign: 'top' }}>
                    {evaluation.dataset_name || 'Unknown Dataset'}
                  </td>
                  <td style={{ padding: '12px', textAlign: 'center', verticalAlign: 'top' }}>
                    <span style={{
                      padding: '4px 8px',
                      borderRadius: '4px',
                      fontSize: '12px',
                      fontWeight: 'bold',
                      color: getStatusDisplay(evaluation.status).color,
                      backgroundColor: getStatusDisplay(evaluation.status).bgColor
                    }}>
                      {getStatusDisplay(evaluation.status).text}
                    </span>
                  </td>
                  <td style={{ padding: '12px', textAlign: 'center', verticalAlign: 'top' }}>
                    <span style={{ fontWeight: 'bold', fontSize: '16px' }}>
                      {formatAccuracy(evaluation.continuous_learning_accuracy)}
                    </span>
                  </td>
                  <td style={{ padding: '12px', textAlign: 'center', verticalAlign: 'top' }}>
                    <span style={{ fontWeight: 'bold', fontSize: '16px' }}>
                      {formatAccuracy(evaluation.driving_for_result_accuracy)}
                    </span>
                  </td>
                  <td style={{ padding: '12px', textAlign: 'center', verticalAlign: 'top' }}>
                    {evaluation.total_rows_processed || 0}
                  </td>
                  <td style={{ padding: '12px', textAlign: 'center', verticalAlign: 'top' }}>
                    <AnnotationSelector
                      evaluationId={evaluation.id}
                      currentAnnotation={evaluation.annotation}
                      onAnnotationUpdate={onAnnotationUpdate}
                      apiService={loiEvaluationsApi}
                    />
                  </td>
                  <td style={{ padding: '12px', textAlign: 'center', verticalAlign: 'top' }}>
                    <button
                      onClick={() => toggleRow(evaluation.id)}
                      style={{
                        padding: '6px 12px',
                        border: '1px solid #007bff',
                        borderRadius: '4px',
                        backgroundColor: expandedRow === evaluation.id ? '#007bff' : 'white',
                        color: expandedRow === evaluation.id ? 'white' : '#007bff',
                        cursor: 'pointer',
                        fontSize: '12px'
                      }}
                    >
                      {expandedRow === evaluation.id ? 'Hide Details' : 'View Details'}
                    </button>
                  </td>
                </tr>

                {/* Expanded row content */}
                {expandedRow === evaluation.id && (
                  <tr>
                    <td colSpan="8" style={{ padding: '0', backgroundColor: '#f8f9fa' }}>
                      <div style={{ padding: '20px' }}>
                        {evaluation.status === 'completed' && evaluation.output && evaluation.output.results ? (
                          <div>
                            <h4 style={{ margin: '0 0 16px 0' }}>Detailed Results:</h4>
                            <div style={{ overflowX: 'auto' }}>
                              <table style={{ width: '100%', borderCollapse: 'collapse', backgroundColor: 'white' }}>
                                <thead>
                                  <tr style={{ backgroundColor: '#e9ecef' }}>
                                    <th style={{ padding: '8px', border: '1px solid #ddd', fontSize: '12px' }}>Row</th>
                                    <th style={{ padding: '8px', border: '1px solid #ddd', fontSize: '12px' }}>Input Text</th>
                                    <th style={{ padding: '8px', border: '1px solid #ddd', fontSize: '12px' }}>Expected CL</th>
                                    <th style={{ padding: '8px', border: '1px solid #ddd', fontSize: '12px' }}>Actual CL</th>
                                    <th style={{ padding: '8px', border: '1px solid #ddd', fontSize: '12px' }}>CL Diff</th>
                                    <th style={{ padding: '8px', border: '1px solid #ddd', fontSize: '12px' }}>Expected DR</th>
                                    <th style={{ padding: '8px', border: '1px solid #ddd', fontSize: '12px' }}>Actual DR</th>
                                    <th style={{ padding: '8px', border: '1px solid #ddd', fontSize: '12px' }}>DR Diff</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  {evaluation.output.results.map((result, index) => (
                                    <tr key={index}>
                                      <td style={{ padding: '8px', border: '1px solid #ddd', textAlign: 'center', fontSize: '12px' }}>
                                        {result.rowIndex}
                                      </td>
                                      <td style={{ padding: '8px', border: '1px solid #ddd', fontSize: '12px' }}>
                                        <button
                                          onClick={() => openInputModal(result.inputText)}
                                          style={{
                                            background: 'none',
                                            border: '1px solid #007bff',
                                            borderRadius: '4px',
                                            color: '#007bff',
                                            cursor: 'pointer',
                                            padding: '4px 8px',
                                            fontSize: '11px'
                                          }}
                                        >
                                          View Text
                                        </button>
                                      </td>
                                      <td style={{ padding: '8px', border: '1px solid #ddd', textAlign: 'center', fontSize: '12px' }}>
                                        {result.expectedContinuousLearning}
                                      </td>
                                      <td style={{ padding: '8px', border: '1px solid #ddd', textAlign: 'center', fontSize: '12px' }}>
                                        {result.actualContinuousLearning}
                                      </td>
                                      <td style={{
                                        padding: '8px',
                                        border: '1px solid #ddd',
                                        textAlign: 'center',
                                        fontSize: '12px',
                                        fontWeight: 'bold',
                                        color: getDiffColor(result.continuousLearningDiff),
                                        backgroundColor: getDiffBackgroundColor(result.continuousLearningDiff)
                                      }}>
                                        {formatDiff(result.continuousLearningDiff)}
                                      </td>
                                      <td style={{ padding: '8px', border: '1px solid #ddd', textAlign: 'center', fontSize: '12px' }}>
                                        {result.expectedDrivingForResult}
                                      </td>
                                      <td style={{ padding: '8px', border: '1px solid #ddd', textAlign: 'center', fontSize: '12px' }}>
                                        {result.actualDrivingForResult}
                                      </td>
                                      <td style={{
                                        padding: '8px',
                                        border: '1px solid #ddd',
                                        textAlign: 'center',
                                        fontSize: '12px',
                                        fontWeight: 'bold',
                                        color: getDiffColor(result.drivingForResultDiff),
                                        backgroundColor: getDiffBackgroundColor(result.drivingForResultDiff)
                                      }}>
                                        {formatDiff(result.drivingForResultDiff)}
                                      </td>
                                    </tr>
                                  ))}
                                </tbody>
                              </table>
                            </div>
                          </div>
                        ) : (
                          <div style={{ textAlign: 'center', color: '#666', fontStyle: 'italic' }}>
                            {evaluation.status === 'error' ? 'Evaluation failed' : 'Processing...'}
                          </div>
                        )}
                      </div>
                    </td>
                  </tr>
                )}
              </React.Fragment>
            ))}
          </tbody>
        </table>
      </div>

      {/* Input Text Modal */}
      <InputTextModal
        isOpen={inputModalState.isOpen}
        onClose={closeInputModal}
        inputText={inputModalState.inputText}
      />
    </div>
  );
};

export default LoIResultsTable;
