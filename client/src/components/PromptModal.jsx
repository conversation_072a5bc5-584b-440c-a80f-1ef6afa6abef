import { useState, useEffect } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';

const PromptModal = ({ isOpen, onClose, promptData, promptName, onSave }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState('');
  const [saving, setSaving] = useState(false);

  // Update edit content when promptData changes
  useEffect(() => {
    if (promptData?.content) {
      setEditContent(promptData.content);
    }
  }, [promptData?.content]);

  // Reset editing state when modal is closed
  useEffect(() => {
    if (!isOpen) {
      setIsEditing(false);
      setSaving(false);
    }
  }, [isOpen]);

  if (!isOpen) return null;

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSave = async () => {
    if (!onSave) return;

    try {
      setSaving(true);
      await onSave(editContent.trim());
      setIsEditing(false);
    } catch (error) {
      console.error('Error saving prompt:', error);
      alert('Failed to save prompt');
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    setEditContent(promptData?.content || '');
    setIsEditing(false);
  };

  return (
    <div
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 1000
      }}
      onClick={onClose}
    >
      <div
        style={{
          backgroundColor: 'white',
          borderRadius: '8px',
          padding: '24px',
          maxWidth: '800px',
          maxHeight: '80vh',
          width: '90%',
          overflow: 'auto',
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
        }}
        onClick={(e) => e.stopPropagation()}
      >
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '16px',
          borderBottom: '1px solid #eee',
          paddingBottom: '16px'
        }}>
          <h2 style={{ margin: 0, color: '#333' }}>
            {promptName} - Version {promptData?.version}
          </h2>
          <button
            onClick={onClose}
            style={{
              background: 'none',
              border: 'none',
              fontSize: '24px',
              cursor: 'pointer',
              color: '#666',
              padding: '0',
              width: '32px',
              height: '32px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            ×
          </button>
        </div>
        
        {promptData ? (
          <div>
            <div style={{
              fontSize: '12px',
              color: '#666',
              marginBottom: '12px'
            }}>
              Last updated: {new Date(promptData.updatedAt).toLocaleString()}
            </div>

            {isEditing ? (
              <div>
                <textarea
                  value={editContent}
                  onChange={(e) => setEditContent(e.target.value)}
                  style={{
                    width: '100%',
                    minHeight: '400px',
                    maxHeight: '768px',
                    padding: '16px',
                    border: '1px solid #e9ecef',
                    borderRadius: '4px',
                    fontFamily: 'monospace',
                    fontSize: '14px',
                    lineHeight: '1.5',
                    resize: 'vertical',
                    outline: 'none'
                  }}
                  placeholder="Enter prompt content..."
                />
              </div>
            ) : (
              <div style={{
                backgroundColor: '#f8f9fa',
                border: '1px solid #e9ecef',
                borderRadius: '4px',
                padding: '16px',
                fontFamily: 'monospace',
                fontSize: '14px',
                lineHeight: '1.5',
                whiteSpace: 'pre',
                maxHeight: '768px',
                overflow: 'auto',
                overflowX: 'auto',
                overflowY: 'auto',
                width: '100%'
              }}>
                <ReactMarkdown remarkPlugins={[remarkGfm]}>{promptData.content}</ReactMarkdown>
              </div>
            )}
          </div>
        ) : (
          <div style={{
            textAlign: 'center',
            padding: '40px',
            color: '#666'
          }}>
            Loading prompt content...
          </div>
        )}
        
        <div style={{
          marginTop: '20px',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <div>
            {/* Left side - Edit button (only show if onSave is provided and not editing) */}
            {onSave && !isEditing && (
              <button
                onClick={handleEdit}
                style={{
                  padding: '8px 16px',
                  backgroundColor: '#28a745',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  marginRight: '8px'
                }}
              >
                ✏️ Edit
              </button>
            )}
          </div>

          <div>
            {/* Right side - Action buttons */}
            {isEditing ? (
              <>
                <button
                  onClick={handleCancel}
                  style={{
                    padding: '8px 16px',
                    backgroundColor: '#6c757d',
                    color: 'white',
                    border: 'none',
                    borderRadius: '4px',
                    cursor: 'pointer',
                    marginRight: '8px'
                  }}
                >
                  Cancel
                </button>
                <button
                  onClick={handleSave}
                  disabled={saving}
                  style={{
                    padding: '8px 16px',
                    backgroundColor: saving ? '#6c757d' : '#007bff',
                    color: 'white',
                    border: 'none',
                    borderRadius: '4px',
                    cursor: saving ? 'not-allowed' : 'pointer'
                  }}
                >
                  {saving ? 'Saving...' : '💾 Save'}
                </button>
              </>
            ) : (
              <button
                onClick={onClose}
                style={{
                  padding: '8px 16px',
                  backgroundColor: '#007bff',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}
              >
                Close
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PromptModal;
